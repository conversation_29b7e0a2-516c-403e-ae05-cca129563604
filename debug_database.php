<?php
// Script de débogage pour vérifier la base de données
require_once 'includes/config.php';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Debug Base de Données</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }
        .debug-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .query { background: #e9ecef; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 Debug Base de Données - Pôle Industrie</h1>";

try {
    // Test de connexion
    echo "<div class='debug-section success'>
            <h2>✅ Connexion à la base de données</h2>
            <p>Connexion réussie à la base de données.</p>
          </div>";
    
    $pdo = getDBConnection();
    
    // Vérification des tables
    echo "<div class='debug-section'>
            <h2>📋 Tables existantes</h2>";
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Tables trouvées : " . count($tables) . "</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul></div>";
    
    // Vérification des formations
    echo "<div class='debug-section'>
            <h2>🎓 Formations dans la base</h2>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM formations");
        $total_formations = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as actives FROM formations WHERE statut = 'active'");
        $formations_actives = $stmt->fetch(PDO::FETCH_ASSOC)['actives'];
        
        echo "<p><strong>Total formations :</strong> $total_formations</p>";
        echo "<p><strong>Formations actives :</strong> $formations_actives</p>";
        
        // Afficher quelques formations
        $stmt = $pdo->query("SELECT id, nom, niveau, secteur, statut FROM formations LIMIT 10");
        $formations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($formations)) {
            echo "<h3>Premières formations :</h3>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Nom</th><th>Niveau</th><th>Secteur</th><th>Statut</th></tr>";
            foreach ($formations as $formation) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($formation['id']) . "</td>";
                echo "<td>" . htmlspecialchars($formation['nom']) . "</td>";
                echo "<td>" . htmlspecialchars($formation['niveau']) . "</td>";
                echo "<td>" . htmlspecialchars($formation['secteur']) . "</td>";
                echo "<td>" . htmlspecialchars($formation['statut']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ Aucune formation trouvée !</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur formations : " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    echo "</div>";
    
    // Vérification des actualités
    echo "<div class='debug-section'>
            <h2>📰 Actualités dans la base</h2>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM actualites");
        $total_actualites = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as publiees FROM actualites WHERE statut = 'publie'");
        $actualites_publiees = $stmt->fetch(PDO::FETCH_ASSOC)['publiees'];
        
        echo "<p><strong>Total actualités :</strong> $total_actualites</p>";
        echo "<p><strong>Actualités publiées :</strong> $actualites_publiees</p>";
        
        // Afficher quelques actualités
        $stmt = $pdo->query("SELECT id, titre, categorie, statut, featured, date_publication FROM actualites LIMIT 10");
        $actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($actualites)) {
            echo "<h3>Premières actualités :</h3>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Titre</th><th>Catégorie</th><th>Statut</th><th>À la une</th><th>Date</th></tr>";
            foreach ($actualites as $actualite) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($actualite['id']) . "</td>";
                echo "<td>" . htmlspecialchars($actualite['titre']) . "</td>";
                echo "<td>" . htmlspecialchars($actualite['categorie']) . "</td>";
                echo "<td>" . htmlspecialchars($actualite['statut']) . "</td>";
                echo "<td>" . ($actualite['featured'] ? 'Oui' : 'Non') . "</td>";
                echo "<td>" . htmlspecialchars($actualite['date_publication']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ Aucune actualité trouvée !</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur actualités : " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    echo "</div>";
    
    // Test des requêtes utilisées dans les pages
    echo "<div class='debug-section'>
            <h2>🔍 Test des requêtes des pages</h2>";
    
    // Requête formations.php
    echo "<h3>Requête formations.php :</h3>";
    echo "<div class='query'>SELECT * FROM formations WHERE statut = 'active' ORDER BY secteur, niveau</div>";
    
    try {
        $stmt = $pdo->query("SELECT * FROM formations WHERE statut = 'active' ORDER BY secteur, niveau");
        $formations_page = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p>✅ Résultats : " . count($formations_page) . " formations trouvées</p>";
        
        if (count($formations_page) > 0) {
            echo "<p>Première formation : " . htmlspecialchars($formations_page[0]['nom']) . "</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur requête formations : " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Requête actualités.php
    echo "<h3>Requête actualités.php :</h3>";
    echo "<div class='query'>SELECT * FROM actualites WHERE statut = 'publie' ORDER BY date_publication DESC</div>";
    
    try {
        $stmt = $pdo->query("SELECT * FROM actualites WHERE statut = 'publie' ORDER BY date_publication DESC");
        $actualites_page = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p>✅ Résultats : " . count($actualites_page) . " actualités trouvées</p>";
        
        if (count($actualites_page) > 0) {
            echo "<p>Première actualité : " . htmlspecialchars($actualites_page[0]['titre']) . "</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur requête actualités : " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Requête actualités à la une
    echo "<h3>Requête actualités à la une :</h3>";
    echo "<div class='query'>SELECT * FROM actualites WHERE statut = 'publie' AND featured = 1 ORDER BY date_publication DESC LIMIT 3</div>";
    
    try {
        $stmt = $pdo->query("SELECT * FROM actualites WHERE statut = 'publie' AND featured = 1 ORDER BY date_publication DESC LIMIT 3");
        $actualites_featured = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p>✅ Résultats : " . count($actualites_featured) . " actualités à la une trouvées</p>";
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Erreur requête actualités featured : " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
    
    // Informations de configuration
    echo "<div class='debug-section'>
            <h2>⚙️ Configuration</h2>";
    
    echo "<p><strong>Fichier config :</strong> includes/config.php</p>";
    echo "<p><strong>Fonction getDBConnection :</strong> " . (function_exists('getDBConnection') ? 'Existe' : 'N\'existe pas') . "</p>";
    
    // Test de la fonction getDBConnection
    if (function_exists('getDBConnection')) {
        try {
            $test_pdo = getDBConnection();
            echo "<p>✅ Fonction getDBConnection() fonctionne</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur getDBConnection() : " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "</div>";
    
    // Actions recommandées
    echo "<div class='debug-section'>
            <h2>🔧 Actions recommandées</h2>";
    
    if ($formations_actives == 0) {
        echo "<p class='error'>❌ Aucune formation active trouvée. Exécutez le script d'installation de la base de données.</p>";
        echo "<p><a href='database/install_database.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Installer la base de données</a></p>";
    }
    
    if ($actualites_publiees == 0) {
        echo "<p class='error'>❌ Aucune actualité publiée trouvée. Exécutez le script d'installation de la base de données.</p>";
    }
    
    if ($formations_actives > 0 && $actualites_publiees > 0) {
        echo "<p class='success'>✅ Les données semblent correctes. Le problème peut venir du code des pages.</p>";
        echo "<p><a href='formations.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Tester Formations</a>";
        echo "<a href='actualites.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Tester Actualités</a></p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='debug-section error'>
            <h2>❌ Erreur de connexion</h2>
            <p><strong>Erreur :</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <p>Vérifiez que :</p>
            <ul>
                <li>MySQL est démarré (XAMPP/WAMP)</li>
                <li>La base de données 'pole_industrie' existe</li>
                <li>Les paramètres de connexion sont corrects</li>
            </ul>
          </div>";
}

echo "</body></html>";
?>
