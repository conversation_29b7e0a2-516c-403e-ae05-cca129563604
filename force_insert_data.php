<?php
// Script pour forcer l'insertion des données
require_once 'includes/config.php';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Force Insert Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #e6f3ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🚀 Force Insert Data</h1>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='info'>Connexion à la base de données réussie</div>";
    
    // Vider les tables d'abord
    echo "<div class='info'>Vidage des tables existantes...</div>";
    $pdo->exec("DELETE FROM formations");
    $pdo->exec("DELETE FROM actualites");
    $pdo->exec("ALTER TABLE formations AUTO_INCREMENT = 1");
    $pdo->exec("ALTER TABLE actualites AUTO_INCREMENT = 1");
    
    // Insérer les formations
    echo "<div class='info'>Insertion des formations...</div>";
    
    $formations = [
        ['CAP Maintenance des Véhicules Automobiles', 'Formation complète en maintenance automobile avec pratique sur véhicules récents. Apprentissage des techniques de diagnostic électronique, mécanique moteur, systèmes de freinage, climatisation et électricité automobile.', '2 ans', 'CAP', 'automobile', 0.00, 20, '2024-09-01', '2026-06-30', 'Atelier Automobile', 'active'],
        ['BTS Électrotechnique', 'Formation en systèmes électriques industriels, domotique et énergies renouvelables. Étude des automatismes, variateurs de vitesse, réseaux industriels, supervision SCADA.', '2 ans', 'BTS', 'electricite', 0.00, 15, '2024-09-01', '2026-06-30', 'Laboratoire Électricité', 'active'],
        ['Formation Continue Soudage', 'Perfectionnement en techniques de soudage industriel sur tous matériaux : acier, inox, aluminium. Apprentissage des procédés TIG, MIG/MAG, soudage orbital.', '3 mois', 'Formation Continue', 'mecanique', 2500.00, 12, '2024-03-01', '2024-05-31', 'Atelier Soudage', 'active'],
        ['Licence Pro Génie Thermique', 'Spécialisation en systèmes de chauffage, climatisation, énergies renouvelables et efficacité énergétique. Étude des pompes à chaleur, systèmes solaires, géothermie.', '1 an', 'Licence Pro', 'thermique', 0.00, 25, '2024-09-01', '2025-06-30', 'Campus Principal', 'active'],
        ['Formation Courte Hydraulique', 'Initiation complète aux systèmes hydrauliques industriels et mobiles. Étude des composants : pompes, vérins, distributeurs, accumulateurs.', '1 mois', 'Formation Courte', 'mecanique', 1200.00, 10, '2024-02-01', '2024-02-29', 'Atelier Hydraulique', 'active'],
        ['Apprentissage Construction Navale', 'Formation complète en alternance aux métiers de la construction navale : chaudronnerie, soudage, assemblage de coques.', '3 ans', 'Apprentissage', 'navale', 0.00, 18, '2024-09-01', '2027-06-30', 'Chantier Naval', 'active']
    ];
    
    $formations_count = 0;
    foreach ($formations as $formation) {
        try {
            $stmt = $pdo->prepare("INSERT INTO formations (nom, description, duree, niveau, secteur, prix, places_disponibles, date_debut, date_fin, lieu, statut, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)");
            $stmt->execute($formation);
            $formations_count++;
        } catch (PDOException $e) {
            echo "<div class='error'>Erreur formation: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div class='success'>✅ $formations_count formations insérées</div>";
    
    // Insérer les actualités
    echo "<div class='info'>Insertion des actualités...</div>";
    
    $actualites = [
        ['Nouvelle Formation en Robotique Industrielle', 'Le Pôle Industrie lance une nouvelle formation dédiée à la robotique industrielle. Cette formation de 6 mois permettra aux participants de maîtriser les technologies robotiques les plus avancées, de la programmation à la maintenance. Les étudiants travailleront sur des robots collaboratifs dernière génération et découvriront les enjeux de l\'industrie 4.0.', 'Découvrez notre nouvelle formation en robotique industrielle, une opportunité unique de se former aux technologies de demain.', 'mecanique', 'publie', 1, '2024-01-15'],
        ['Partenariat Stratégique avec Renault', 'Nous sommes fiers d\'annoncer notre nouveau partenariat avec Renault pour la formation de techniciens automobiles. Ce partenariat permettra à nos étudiants d\'accéder à des équipements de pointe, des stages en entreprise et des débouchés garantis.', 'Un nouveau partenariat stratégique avec Renault pour renforcer nos formations automobiles.', 'automobile', 'publie', 1, '2024-01-10'],
        ['Journée Portes Ouvertes 2024', 'Plus de 500 visiteurs ont participé à notre journée portes ouvertes le 15 mars 2024. Au programme : visites des ateliers, démonstrations techniques, rencontres avec les formateurs et témoignages d\'anciens étudiants.', 'Retour sur notre journée portes ouvertes qui a attiré plus de 500 visiteurs.', 'general', 'publie', 0, '2024-03-16'],
        ['Innovation en Génie Thermique', 'Nos étudiants en génie thermique ont développé un système de chauffage innovant qui réduit la consommation énergétique de 30%. Ce projet révolutionnaire utilise l\'intelligence artificielle pour optimiser les performances thermiques.', 'Un projet étudiant révolutionnaire en génie thermique remporte un prix d\'innovation.', 'thermique', 'publie', 1, '2024-01-01'],
        ['Nouveau Laboratoire d\'Électronique', 'Inauguration de notre nouveau laboratoire d\'électronique embarquée équipé des dernières technologies pour la formation aux systèmes automobiles modernes.', 'Un nouveau laboratoire de pointe pour former aux technologies automobiles.', 'electricite', 'publie', 0, '2024-02-20'],
        ['Salon Nautique International', 'Les étudiants de la section navale ont présenté leurs projets au salon nautique international. Leurs innovations en matière de propulsion écologique ont été remarquées.', 'Nos étudiants brillent au salon nautique avec leurs innovations.', 'navale', 'publie', 0, '2024-02-15']
    ];
    
    $actualites_count = 0;
    foreach ($actualites as $actualite) {
        try {
            $stmt = $pdo->prepare("INSERT INTO actualites (titre, contenu, extrait, categorie, statut, featured, date_publication, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, 1)");
            $stmt->execute($actualite);
            $actualites_count++;
        } catch (PDOException $e) {
            echo "<div class='error'>Erreur actualité: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div class='success'>✅ $actualites_count actualités insérées</div>";
    
    // Vérification finale
    $stmt = $pdo->query("SELECT COUNT(*) FROM formations WHERE statut = 'active'");
    $total_formations = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM actualites WHERE statut = 'publie'");
    $total_actualites = $stmt->fetchColumn();
    
    echo "<div class='success'>
            <h2>✅ Insertion terminée !</h2>
            <p>Formations actives : $total_formations</p>
            <p>Actualités publiées : $total_actualites</p>
          </div>";
    
    echo "<div class='info'>
            <h3>Testez maintenant :</h3>
            <p><a href='formations.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Page Formations</a></p>
            <p><a href='actualites.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Page Actualités</a></p>
            <p><a href='test_formations.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Test Formations</a></p>
            <p><a href='test_actualites.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Test Actualités</a></p>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur : " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</body></html>";
?>
