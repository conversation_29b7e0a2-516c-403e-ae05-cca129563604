<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification et les droits admin
requireRole('admin');

$page_title = "Gestion des utilisateurs";
$current_user = getCurrentUser();

// Gestion des actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

try {
    $pdo = getDBConnection();
    
    // Traitement des actions POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['bulk_action']) && isset($_POST['selected_items'])) {
            // Actions en lot
            $bulk_action = $_POST['bulk_action'];
            $selected_items = $_POST['selected_items'];
            
            foreach ($selected_items as $item_id) {
                // Empêcher la suppression de son propre compte
                if ($item_id == $current_user['id'] && $bulk_action === 'delete') {
                    continue;
                }
                
                switch ($bulk_action) {
                    case 'promote_admin':
                        $stmt = $pdo->prepare("UPDATE admin_users SET role = 'admin' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'demote_editor':
                        $stmt = $pdo->prepare("UPDATE admin_users SET role = 'editor' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'delete':
                        $stmt = $pdo->prepare("DELETE FROM admin_users WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                }
            }
            $message = "Actions appliquées avec succès";
        }
        
        // Sauvegarde d'utilisateur
        if (isset($_POST['save_user'])) {
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $role = $_POST['role'] ?? 'editor';
            
            if (empty($username) || empty($email)) {
                $error = "Le nom d'utilisateur et l'email sont obligatoires";
            } elseif ($action === 'add' && empty($password)) {
                $error = "Le mot de passe est obligatoire pour un nouvel utilisateur";
            } else {
                try {
                    if ($action === 'add') {
                        // Vérifier l'unicité
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = ? OR email = ?");
                        $stmt->execute([$username, $email]);
                        if ($stmt->fetchColumn() > 0) {
                            $error = "Ce nom d'utilisateur ou cet email existe déjà";
                        } else {
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("
                                INSERT INTO admin_users (username, email, password, role) 
                                VALUES (?, ?, ?, ?)
                            ");
                            $stmt->execute([$username, $email, $hashed_password, $role]);
                            $message = "Utilisateur créé avec succès";
                        }
                    } else {
                        // Modification
                        if (!empty($password)) {
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("
                                UPDATE admin_users 
                                SET username = ?, email = ?, password = ?, role = ?
                                WHERE id = ?
                            ");
                            $stmt->execute([$username, $email, $hashed_password, $role, $id]);
                        } else {
                            $stmt = $pdo->prepare("
                                UPDATE admin_users 
                                SET username = ?, email = ?, role = ?
                                WHERE id = ?
                            ");
                            $stmt->execute([$username, $email, $role, $id]);
                        }
                        $message = "Utilisateur modifié avec succès";
                    }
                    
                    if (!$error) {
                        header("Location: utilisateurs.php?message=" . urlencode($message));
                        exit;
                    }
                    
                } catch (PDOException $e) {
                    if ($e->getCode() == 23000) {
                        $error = "Ce nom d'utilisateur ou cet email existe déjà";
                    } else {
                        $error = "Erreur lors de la sauvegarde : " . $e->getMessage();
                    }
                }
            }
        }
    }
    
    // Traitement des actions GET
    if ($action === 'delete' && $id) {
        if ($id == $current_user['id']) {
            $error = "Vous ne pouvez pas supprimer votre propre compte";
        } else {
            $stmt = $pdo->prepare("DELETE FROM admin_users WHERE id = ?");
            $stmt->execute([$id]);
            $message = "Utilisateur supprimé avec succès";
        }
        $action = 'list';
    }
    
    // Récupération des utilisateurs pour la liste
    if ($action === 'list') {
        $filter_role = $_GET['role'] ?? '';
        $search = $_GET['search'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if ($filter_role) {
            $where_conditions[] = "role = ?";
            $params[] = $filter_role;
        }
        
        if ($search) {
            $where_conditions[] = "(username LIKE ? OR email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $stmt = $pdo->prepare("
            SELECT * FROM admin_users 
            $where_clause 
            ORDER BY created_at DESC
        ");
        $stmt->execute($params);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = "Erreur de base de données : " . $e->getMessage();
}

include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success" data-dismiss="auto">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <div class="page-header">
        <div class="page-title">
            <h1>Gestion des utilisateurs</h1>
            <p>Gérez les comptes administrateurs et éditeurs</p>
        </div>
        <div class="page-actions">
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Ajouter un utilisateur
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="filters-bar">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text" name="search" placeholder="Rechercher..." 
                       value="<?php echo htmlspecialchars($search ?? ''); ?>">
            </div>
            
            <div class="filter-group">
                <select name="role">
                    <option value="">Tous les rôles</option>
                    <option value="admin" <?php echo ($filter_role === 'admin') ? 'selected' : ''; ?>>Administrateur</option>
                    <option value="editor" <?php echo ($filter_role === 'editor') ? 'selected' : ''; ?>>Éditeur</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-search"></i>
                Filtrer
            </button>
        </form>
    </div>
    
    <!-- Actions en lot -->
    <form method="POST" id="bulk-form">
        <div class="bulk-actions" style="display: none;">
            <select name="bulk_action">
                <option value="">Actions en lot</option>
                <option value="promote_admin">Promouvoir administrateur</option>
                <option value="demote_editor">Rétrograder éditeur</option>
                <option value="delete">Supprimer</option>
            </select>
            <button type="submit" class="btn btn-secondary">Appliquer</button>
        </div>
        
        <!-- Tableau des utilisateurs -->
        <div class="data-table">
            <table>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all"></th>
                        <th>Utilisateur</th>
                        <th>Email</th>
                        <th>Rôle</th>
                        <th>Dernière connexion</th>
                        <th>Créé le</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="7" class="no-data">
                                <i class="fas fa-users"></i>
                                <p>Aucun utilisateur trouvé</p>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($users as $user): ?>
                            <tr class="data-row">
                                <td>
                                    <input type="checkbox" name="selected_items[]" value="<?php echo $user['id']; ?>" 
                                           class="item-checkbox" <?php echo ($user['id'] == $current_user['id']) ? 'disabled' : ''; ?>>
                                </td>
                                <td>
                                    <div class="item-title">
                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                        <?php if ($user['id'] == $current_user['id']): ?>
                                            <span class="badge current-user">Vous</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <span class="role-badge <?php echo $user['role']; ?>">
                                        <?php echo formatRole($user['role']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Jamais</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=edit&id=<?php echo $user['id']; ?>" class="btn-icon" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($user['id'] != $current_user['id']): ?>
                                            <a href="?action=delete&id=<?php echo $user['id']; ?>" class="btn-icon btn-danger" 
                                               data-action="delete" data-item="<?php echo htmlspecialchars($user['username']); ?>" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </form>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <?php
    $user = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
        $stmt->execute([$id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            $error = "Utilisateur non trouvé";
            $action = 'list';
        }
    }
    ?>

    <div class="page-header">
        <div class="page-title">
            <h1><?php echo $action === 'add' ? 'Ajouter' : 'Modifier'; ?> un utilisateur</h1>
            <p>Créez ou modifiez un compte administrateur</p>
        </div>
        <div class="page-actions">
            <a href="utilisateurs.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Retour à la liste
            </a>
        </div>
    </div>

    <form method="POST" class="form-container">
        <div class="form-grid">
            <div class="form-main">
                <div class="form-section">
                    <h3>Informations du compte</h3>

                    <div class="form-group">
                        <label for="username">Nom d'utilisateur *</label>
                        <input type="text" id="username" name="username" required
                               value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>"
                               placeholder="Nom d'utilisateur unique">
                    </div>

                    <div class="form-group">
                        <label for="email">Adresse email *</label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>"
                               placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="password">
                            Mot de passe <?php echo $action === 'add' ? '*' : '(laisser vide pour ne pas changer)'; ?>
                        </label>
                        <input type="password" id="password" name="password"
                               <?php echo $action === 'add' ? 'required' : ''; ?>
                               placeholder="<?php echo $action === 'add' ? 'Mot de passe sécurisé' : 'Nouveau mot de passe'; ?>">
                        <?php if ($action === 'edit'): ?>
                            <small class="form-help">Laissez vide pour conserver le mot de passe actuel</small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="form-sidebar">
                <div class="form-section">
                    <h3>Permissions</h3>

                    <div class="form-group">
                        <label for="role">Rôle</label>
                        <select id="role" name="role">
                            <option value="editor" <?php echo ($user['role'] ?? 'editor') === 'editor' ? 'selected' : ''; ?>>Éditeur</option>
                            <option value="admin" <?php echo ($user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Administrateur</option>
                        </select>
                        <small class="form-help">
                            <strong>Éditeur :</strong> Peut gérer le contenu (actualités, formations, événements)<br>
                            <strong>Administrateur :</strong> Accès complet à toutes les fonctionnalités
                        </small>
                    </div>
                </div>

                <?php if ($action === 'edit' && $user): ?>
                <div class="form-section">
                    <h3>Informations</h3>

                    <div class="info-item">
                        <label>Créé le :</label>
                        <span><?php echo date('d/m/Y à H:i', strtotime($user['created_at'])); ?></span>
                    </div>

                    <div class="info-item">
                        <label>Dernière connexion :</label>
                        <span>
                            <?php if ($user['last_login']): ?>
                                <?php echo date('d/m/Y à H:i', strtotime($user['last_login'])); ?>
                            <?php else: ?>
                                Jamais connecté
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <div class="form-actions">
                    <button type="submit" name="save_user" class="btn btn-primary btn-full">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer' : 'Mettre à jour'; ?>
                    </button>

                    <?php if ($action === 'edit' && $user && $user['id'] != $current_user['id']): ?>
                        <a href="utilisateurs.php?action=delete&id=<?php echo $id; ?>"
                           class="btn btn-danger btn-full"
                           data-action="delete" data-item="<?php echo htmlspecialchars($user['username']); ?>">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </form>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
