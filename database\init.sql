-- Base de données pour le Pôle Industrie
-- Création des tables pour l'administration

-- Table des utilisateurs administrateurs
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Table des actualités
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    extrait TEXT,
    image VARCHAR(255),
    categorie ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') DEFAULT 'general',
    statut ENUM('brouillon', 'publie', 'archive') DEFAULT 'brouillon',
    featured BOOLEAN DEFAULT FALSE,
    date_publication DATE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Table des formations
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duree VARCHAR(50),
    niveau ENUM('CAP', 'BTS', 'Licence Pro', 'Formation Continue', 'Formation Courte', 'Apprentissage') NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    prix DECIMAL(10,2),
    places_disponibles INT DEFAULT 0,
    date_debut DATE,
    date_fin DATE,
    lieu VARCHAR(255),
    statut ENUM('active', 'inactive', 'complete') DEFAULT 'active',
    image VARCHAR(255),
    brochure VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Table des événements
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME,
    lieu VARCHAR(255),
    type_evenement ENUM('conference', 'salon', 'portes_ouvertes', 'formation', 'autre') DEFAULT 'autre',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    places_max INT,
    places_reservees INT DEFAULT 0,
    prix DECIMAL(10,2) DEFAULT 0,
    image VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Table des emplois du temps
CREATE TABLE IF NOT EXISTS emplois_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    date_cours DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    salle VARCHAR(100),
    formateur VARCHAR(255),
    type_cours ENUM('cours', 'tp', 'td', 'examen', 'stage') DEFAULT 'cours',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Table des secteurs (pour personnalisation)
CREATE TABLE IF NOT EXISTS secteurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    couleur VARCHAR(7) DEFAULT '#1e3a8a',
    icone VARCHAR(50) DEFAULT 'fas fa-cog',
    ordre INT DEFAULT 0,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertion des secteurs par défaut
INSERT INTO secteurs (nom, slug, description, couleur, icone, ordre) VALUES
('Pôle Industrie de la Mécanique', 'mecanique', 'Formation aux métiers de la mécanique industrielle et de précision', '#1e3a8a', 'fas fa-cog', 1),
('Pôle Industrie de l\'Électricité', 'electricite', 'Expertise en systèmes électriques et électroniques industriels', '#dc2626', 'fas fa-bolt', 2),
('Pôle Génie Thermique', 'thermique', 'Spécialisation en systèmes de chauffage et climatisation', '#7c3aed', 'fas fa-thermometer-half', 3),
('Pôle Industrie de l\'Automobile', 'automobile', 'Formation aux technologies automobiles modernes', '#0ea5e9', 'fas fa-car', 4),
('Pôle Industrie Navale', 'navale', 'Métiers de la construction et réparation navale', '#0d9488', 'fas fa-ship', 5),
('Pôle Métiers de l\'Industrie', 'general', 'Formation transversale aux métiers industriels', '#16a34a', 'fas fa-tools', 6);

-- Insertion d'un utilisateur admin par défaut (mot de passe: admin123)
INSERT INTO admin_users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');
