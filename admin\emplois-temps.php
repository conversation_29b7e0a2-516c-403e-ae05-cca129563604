<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification
requireLogin();

$page_title = "Gestion des emplois du temps";
$current_user = getCurrentUser();

// Gestion des actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

try {
    $pdo = getDBConnection();
    
    // Traitement des actions POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['bulk_action']) && isset($_POST['selected_items'])) {
            // Actions en lot
            $bulk_action = $_POST['bulk_action'];
            $selected_items = $_POST['selected_items'];
            
            foreach ($selected_items as $item_id) {
                switch ($bulk_action) {
                    case 'planifie':
                        $stmt = $pdo->prepare("UPDATE emplois_temps SET statut = 'planifie' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'annule':
                        $stmt = $pdo->prepare("UPDATE emplois_temps SET statut = 'annule' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'delete':
                        $stmt = $pdo->prepare("DELETE FROM emplois_temps WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                }
            }
            $message = "Actions appliquées avec succès";
        }
        
        // Sauvegarde de cours
        if (isset($_POST['save_cours'])) {
            $formation_id = $_POST['formation_id'] ?? null;
            $titre = trim($_POST['titre'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $date_cours = $_POST['date_cours'] ?? '';
            $heure_debut = $_POST['heure_debut'] ?? '';
            $heure_fin = $_POST['heure_fin'] ?? '';
            $salle = trim($_POST['salle'] ?? '');
            $formateur = trim($_POST['formateur'] ?? '');
            $type_cours = $_POST['type_cours'] ?? 'cours';
            $statut = $_POST['statut'] ?? 'planifie';
            
            if (empty($titre) || empty($date_cours) || empty($heure_debut) || empty($heure_fin)) {
                $error = "Le titre, la date, l'heure de début et de fin sont obligatoires";
            } else {
                try {
                    if ($action === 'add') {
                        $stmt = $pdo->prepare("
                            INSERT INTO emplois_temps (formation_id, titre, description, date_cours, heure_debut, heure_fin, salle, formateur, type_cours, statut, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$formation_id, $titre, $description, $date_cours, $heure_debut, $heure_fin, $salle, $formateur, $type_cours, $statut, $current_user['id']]);
                        $message = "Cours ajouté avec succès";
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE emplois_temps 
                            SET formation_id = ?, titre = ?, description = ?, date_cours = ?, heure_debut = ?, heure_fin = ?, salle = ?, formateur = ?, type_cours = ?, statut = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$formation_id, $titre, $description, $date_cours, $heure_debut, $heure_fin, $salle, $formateur, $type_cours, $statut, $id]);
                        $message = "Cours modifié avec succès";
                    }
                    
                    header("Location: emplois-temps.php?message=" . urlencode($message));
                    exit;
                    
                } catch (PDOException $e) {
                    $error = "Erreur lors de la sauvegarde : " . $e->getMessage();
                }
            }
        }
    }
    
    // Traitement des actions GET
    if ($action === 'delete' && $id) {
        $stmt = $pdo->prepare("DELETE FROM emplois_temps WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Cours supprimé avec succès";
        $action = 'list';
    }
    
    // Récupération des formations pour les sélecteurs
    $stmt = $pdo->query("SELECT id, nom FROM formations WHERE statut = 'active' ORDER BY nom");
    $formations_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Récupération des cours pour la liste
    if ($action === 'list') {
        $filter_formation = $_GET['formation'] ?? '';
        $filter_status = $_GET['status'] ?? '';
        $filter_type = $_GET['type'] ?? '';
        $filter_date = $_GET['date'] ?? '';
        $search = $_GET['search'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if ($filter_formation) {
            $where_conditions[] = "et.formation_id = ?";
            $params[] = $filter_formation;
        }
        
        if ($filter_status) {
            $where_conditions[] = "et.statut = ?";
            $params[] = $filter_status;
        }
        
        if ($filter_type) {
            $where_conditions[] = "et.type_cours = ?";
            $params[] = $filter_type;
        }
        
        if ($filter_date) {
            $where_conditions[] = "et.date_cours = ?";
            $params[] = $filter_date;
        }
        
        if ($search) {
            $where_conditions[] = "(et.titre LIKE ? OR et.description LIKE ? OR et.formateur LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $stmt = $pdo->prepare("
            SELECT et.*, f.nom as formation_nom, u.username as created_by_name 
            FROM emplois_temps et 
            LEFT JOIN formations f ON et.formation_id = f.id
            LEFT JOIN admin_users u ON et.created_by = u.id 
            $where_clause 
            ORDER BY et.date_cours ASC, et.heure_debut ASC
        ");
        $stmt->execute($params);
        $cours = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = "Erreur de base de données : " . $e->getMessage();
}

include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success" data-dismiss="auto">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <div class="page-header">
        <div class="page-title">
            <h1>Gestion des emplois du temps</h1>
            <p>Planifiez et organisez les cours et activités pédagogiques</p>
        </div>
        <div class="page-actions">
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Ajouter un cours
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="filters-bar">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text" name="search" placeholder="Rechercher..." 
                       value="<?php echo htmlspecialchars($search ?? ''); ?>">
            </div>
            
            <div class="filter-group">
                <select name="formation">
                    <option value="">Toutes les formations</option>
                    <?php foreach ($formations_list as $formation): ?>
                        <option value="<?php echo $formation['id']; ?>" <?php echo ($filter_formation == $formation['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($formation['nom']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="filter-group">
                <select name="type">
                    <option value="">Tous les types</option>
                    <option value="cours" <?php echo ($filter_type === 'cours') ? 'selected' : ''; ?>>Cours</option>
                    <option value="tp" <?php echo ($filter_type === 'tp') ? 'selected' : ''; ?>>TP</option>
                    <option value="td" <?php echo ($filter_type === 'td') ? 'selected' : ''; ?>>TD</option>
                    <option value="examen" <?php echo ($filter_type === 'examen') ? 'selected' : ''; ?>>Examen</option>
                    <option value="stage" <?php echo ($filter_type === 'stage') ? 'selected' : ''; ?>>Stage</option>
                </select>
            </div>
            
            <div class="filter-group">
                <input type="date" name="date" value="<?php echo htmlspecialchars($filter_date ?? ''); ?>">
            </div>
            
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-search"></i>
                Filtrer
            </button>
        </form>
    </div>
    
    <!-- Actions en lot -->
    <form method="POST" id="bulk-form">
        <div class="bulk-actions" style="display: none;">
            <select name="bulk_action">
                <option value="">Actions en lot</option>
                <option value="planifie">Marquer comme planifié</option>
                <option value="annule">Annuler</option>
                <option value="delete">Supprimer</option>
            </select>
            <button type="submit" class="btn btn-secondary">Appliquer</button>
        </div>
        
        <!-- Tableau des cours -->
        <div class="data-table">
            <table>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all"></th>
                        <th>Cours</th>
                        <th>Formation</th>
                        <th>Date/Heure</th>
                        <th>Type</th>
                        <th>Salle</th>
                        <th>Formateur</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($cours)): ?>
                        <tr>
                            <td colspan="9" class="no-data">
                                <i class="fas fa-clock"></i>
                                <p>Aucun cours trouvé</p>
                                <a href="?action=add" class="btn btn-primary">Ajouter le premier cours</a>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($cours as $c): ?>
                            <tr class="data-row" data-status="<?php echo $c['statut']; ?>">
                                <td>
                                    <input type="checkbox" name="selected_items[]" value="<?php echo $c['id']; ?>" class="item-checkbox">
                                </td>
                                <td>
                                    <div class="item-title">
                                        <strong><?php echo htmlspecialchars($c['titre']); ?></strong>
                                        <?php if ($c['description']): ?>
                                            <div class="item-meta"><?php echo htmlspecialchars(substr($c['description'], 0, 50)) . '...'; ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($c['formation_nom']): ?>
                                        <span class="formation-link"><?php echo htmlspecialchars($c['formation_nom']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">Aucune formation</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="date-info">
                                        <strong><?php echo date('d/m/Y', strtotime($c['date_cours'])); ?></strong><br>
                                        <small><?php echo date('H:i', strtotime($c['heure_debut'])); ?> - <?php echo date('H:i', strtotime($c['heure_fin'])); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="type-badge <?php echo $c['type_cours']; ?>">
                                        <?php echo strtoupper($c['type_cours']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($c['salle'] ?? 'Non définie'); ?></td>
                                <td><?php echo htmlspecialchars($c['formateur'] ?? 'Non assigné'); ?></td>
                                <td>
                                    <span class="status-badge <?php echo $c['statut']; ?>">
                                        <?php echo ucfirst($c['statut']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=edit&id=<?php echo $c['id']; ?>" class="btn-icon" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="?action=delete&id=<?php echo $c['id']; ?>" class="btn-icon btn-danger" 
                                           data-action="delete" data-item="<?php echo htmlspecialchars($c['titre']); ?>" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </form>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <?php
    $cours = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM emplois_temps WHERE id = ?");
        $stmt->execute([$id]);
        $cours = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$cours) {
            $error = "Cours non trouvé";
            $action = 'list';
        }
    }
    ?>

    <div class="page-header">
        <div class="page-title">
            <h1><?php echo $action === 'add' ? 'Ajouter' : 'Modifier'; ?> un cours</h1>
            <p>Planifiez une nouvelle séance dans l'emploi du temps</p>
        </div>
        <div class="page-actions">
            <a href="emplois-temps.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Retour à la liste
            </a>
        </div>
    </div>

    <form method="POST" class="form-container" data-autosave="cours_<?php echo $id ?? 'new'; ?>">
        <div class="form-grid">
            <div class="form-main">
                <div class="form-section">
                    <h3>Informations du cours</h3>

                    <div class="form-group">
                        <label for="titre">Titre du cours *</label>
                        <input type="text" id="titre" name="titre" required
                               value="<?php echo htmlspecialchars($cours['titre'] ?? ''); ?>"
                               placeholder="Ex: Introduction à la mécanique industrielle">
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4"
                                  placeholder="Description du contenu du cours, objectifs..."><?php echo htmlspecialchars($cours['description'] ?? ''); ?></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="formateur">Formateur</label>
                            <input type="text" id="formateur" name="formateur"
                                   value="<?php echo htmlspecialchars($cours['formateur'] ?? ''); ?>"
                                   placeholder="Nom du formateur">
                        </div>

                        <div class="form-group">
                            <label for="salle">Salle</label>
                            <input type="text" id="salle" name="salle"
                                   value="<?php echo htmlspecialchars($cours['salle'] ?? ''); ?>"
                                   placeholder="Ex: Atelier A, Salle 101">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Planification</h3>

                    <div class="form-group">
                        <label for="date_cours">Date du cours *</label>
                        <input type="date" id="date_cours" name="date_cours" required
                               value="<?php echo $cours['date_cours'] ?? ''; ?>">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="heure_debut">Heure de début *</label>
                            <input type="time" id="heure_debut" name="heure_debut" required
                                   value="<?php echo $cours['heure_debut'] ?? ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="heure_fin">Heure de fin *</label>
                            <input type="time" id="heure_fin" name="heure_fin" required
                                   value="<?php echo $cours['heure_fin'] ?? ''; ?>">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-sidebar">
                <div class="form-section">
                    <h3>Formation associée</h3>

                    <div class="form-group">
                        <label for="formation_id">Formation</label>
                        <select id="formation_id" name="formation_id">
                            <option value="">Aucune formation spécifique</option>
                            <?php foreach ($formations_list as $formation): ?>
                                <option value="<?php echo $formation['id']; ?>"
                                        <?php echo ($cours['formation_id'] ?? '') == $formation['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($formation['nom']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Type et statut</h3>

                    <div class="form-group">
                        <label for="type_cours">Type de cours</label>
                        <select id="type_cours" name="type_cours">
                            <option value="cours" <?php echo ($cours['type_cours'] ?? 'cours') === 'cours' ? 'selected' : ''; ?>>Cours magistral</option>
                            <option value="tp" <?php echo ($cours['type_cours'] ?? '') === 'tp' ? 'selected' : ''; ?>>Travaux pratiques</option>
                            <option value="td" <?php echo ($cours['type_cours'] ?? '') === 'td' ? 'selected' : ''; ?>>Travaux dirigés</option>
                            <option value="examen" <?php echo ($cours['type_cours'] ?? '') === 'examen' ? 'selected' : ''; ?>>Examen</option>
                            <option value="stage" <?php echo ($cours['type_cours'] ?? '') === 'stage' ? 'selected' : ''; ?>>Stage</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="statut">Statut</label>
                        <select id="statut" name="statut">
                            <option value="planifie" <?php echo ($cours['statut'] ?? 'planifie') === 'planifie' ? 'selected' : ''; ?>>Planifié</option>
                            <option value="en_cours" <?php echo ($cours['statut'] ?? '') === 'en_cours' ? 'selected' : ''; ?>>En cours</option>
                            <option value="termine" <?php echo ($cours['statut'] ?? '') === 'termine' ? 'selected' : ''; ?>>Terminé</option>
                            <option value="annule" <?php echo ($cours['statut'] ?? '') === 'annule' ? 'selected' : ''; ?>>Annulé</option>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" name="save_cours" class="btn btn-primary btn-full">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer' : 'Mettre à jour'; ?>
                    </button>

                    <?php if ($action === 'edit'): ?>
                        <a href="emplois-temps.php?action=delete&id=<?php echo $id; ?>"
                           class="btn btn-danger btn-full"
                           data-action="delete" data-item="<?php echo htmlspecialchars($cours['titre']); ?>">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </form>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
