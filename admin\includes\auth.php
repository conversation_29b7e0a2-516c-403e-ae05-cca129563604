<?php
// Fichier de gestion de l'authentification pour l'administration

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Vérifier si l'utilisateur est connecté
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Vérifier si l'utilisateur a le rôle requis
 */
function hasRole($required_role) {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['admin_role'] ?? '';
    
    // L'admin a tous les droits
    if ($user_role === 'admin') {
        return true;
    }
    
    // Vérifier le rôle spécifique
    return $user_role === $required_role;
}

/**
 * Rediriger vers la page de connexion si non connecté
 */
function requireLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * Rediriger vers la page de connexion si rôle insuffisant
 */
function requireRole($required_role) {
    requireLogin();
    
    if (!hasRole($required_role)) {
        header('Location: index.php?error=access_denied');
        exit;
    }
}

/**
 * Obtenir les informations de l'utilisateur connecté
 */
function getCurrentUser() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['admin_id'] ?? null,
        'username' => $_SESSION['admin_username'] ?? '',
        'email' => $_SESSION['admin_email'] ?? '',
        'role' => $_SESSION['admin_role'] ?? ''
    ];
}

/**
 * Déconnecter l'utilisateur
 */
function logout() {
    // Détruire toutes les variables de session
    $_SESSION = array();
    
    // Détruire le cookie de session si il existe
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Détruire la session
    session_destroy();
    
    // Rediriger vers la page de connexion
    header('Location: login.php');
    exit;
}

/**
 * Générer un token CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Vérifier un token CSRF
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Obtenir le nom d'affichage de l'utilisateur
 */
function getDisplayName() {
    $user = getCurrentUser();
    return $user ? $user['username'] : 'Utilisateur';
}

/**
 * Vérifier si l'utilisateur est admin
 */
function isAdmin() {
    return hasRole('admin');
}

/**
 * Formater le rôle pour l'affichage
 */
function formatRole($role) {
    switch ($role) {
        case 'admin':
            return 'Administrateur';
        case 'editor':
            return 'Éditeur';
        default:
            return 'Utilisateur';
    }
}
?>
