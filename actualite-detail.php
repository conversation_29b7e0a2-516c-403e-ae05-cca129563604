<?php
// Récupération de l'ID de l'actualité
$actualite_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$actualite_id) {
    header('Location: actualites.php');
    exit;
}

// Récupération des détails de l'actualité
$actualite = null;
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM actualites WHERE id = ? AND statut = 'publie'");
    $stmt->execute([$actualite_id]);
    $actualite = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$actualite) {
        header('Location: actualites.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: actualites.php');
    exit;
}

$page_title = $actualite['titre'];
$page_description = $actualite['extrait'];

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <div class="breadcrumb">
                <a href="index.php">Accueil</a>
                <i class="fas fa-chevron-right"></i>
                <a href="actualites.php">Actualités</a>
                <i class="fas fa-chevron-right"></i>
                <span><?php echo htmlspecialchars($actualite['titre']); ?></span>
            </div>
            <h1><?php echo htmlspecialchars($actualite['titre']); ?></h1>
            <div class="article-meta">
                <span class="article-date">
                    <i class="fas fa-calendar"></i>
                    <?php echo date('d F Y', strtotime($actualite['date_publication'])); ?>
                </span>
                <span class="article-category <?php echo htmlspecialchars($actualite['categorie']); ?>">
                    <i class="fas fa-<?php 
                        $icons = [
                            'mecanique' => 'cog',
                            'electricite' => 'bolt', 
                            'automobile' => 'car',
                            'navale' => 'ship',
                            'thermique' => 'thermometer-half',
                            'general' => 'newspaper'
                        ];
                        echo $icons[$actualite['categorie']] ?? 'newspaper';
                    ?>"></i>
                    <?php 
                    $categories = [
                        'mecanique' => 'Mécanique',
                        'electricite' => 'Électricité', 
                        'automobile' => 'Automobile',
                        'navale' => 'Naval',
                        'thermique' => 'Thermique',
                        'general' => 'Général'
                    ];
                    echo $categories[$actualite['categorie']] ?? 'Actualité';
                    ?>
                </span>
                <?php if ($actualite['featured']): ?>
                    <span class="article-featured">
                        <i class="fas fa-star"></i> À la une
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Contenu de l'article -->
<section class="section">
    <div class="container">
        <div class="article-layout">
            <!-- Contenu principal -->
            <article class="article-content">
                <div class="article-excerpt">
                    <p class="lead"><?php echo htmlspecialchars($actualite['extrait']); ?></p>
                </div>
                
                <div class="article-body">
                    <?php echo nl2br(htmlspecialchars($actualite['contenu'])); ?>
                </div>
                
                <!-- Actions de partage -->
                <div class="article-actions">
                    <h3>Partager cet article</h3>
                    <div class="share-buttons">
                        <button class="share-btn facebook" onclick="shareOnFacebook()">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </button>
                        <button class="share-btn twitter" onclick="shareOnTwitter()">
                            <i class="fab fa-twitter"></i> Twitter
                        </button>
                        <button class="share-btn linkedin" onclick="shareOnLinkedIn()">
                            <i class="fab fa-linkedin-in"></i> LinkedIn
                        </button>
                        <button class="share-btn email" onclick="shareByEmail()">
                            <i class="fas fa-envelope"></i> Email
                        </button>
                        <button class="share-btn copy" onclick="copyLink()">
                            <i class="fas fa-link"></i> Copier le lien
                        </button>
                    </div>
                </div>
                
                <!-- Navigation entre articles -->
                <div class="article-navigation">
                    <?php
                    // Article précédent
                    try {
                        $stmt = $pdo->prepare("SELECT id, titre FROM actualites WHERE statut = 'publie' AND date_publication < ? ORDER BY date_publication DESC LIMIT 1");
                        $stmt->execute([$actualite['date_publication']]);
                        $prev_article = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        // Article suivant
                        $stmt = $pdo->prepare("SELECT id, titre FROM actualites WHERE statut = 'publie' AND date_publication > ? ORDER BY date_publication ASC LIMIT 1");
                        $stmt->execute([$actualite['date_publication']]);
                        $next_article = $stmt->fetch(PDO::FETCH_ASSOC);
                    } catch (PDOException $e) {
                        $prev_article = null;
                        $next_article = null;
                    }
                    ?>
                    
                    <div class="nav-links">
                        <?php if ($prev_article): ?>
                            <a href="actualite-detail.php?id=<?php echo $prev_article['id']; ?>" class="nav-link prev">
                                <i class="fas fa-chevron-left"></i>
                                <div>
                                    <span class="nav-label">Article précédent</span>
                                    <span class="nav-title"><?php echo htmlspecialchars($prev_article['titre']); ?></span>
                                </div>
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($next_article): ?>
                            <a href="actualite-detail.php?id=<?php echo $next_article['id']; ?>" class="nav-link next">
                                <div>
                                    <span class="nav-label">Article suivant</span>
                                    <span class="nav-title"><?php echo htmlspecialchars($next_article['titre']); ?></span>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </article>
            
            <!-- Sidebar -->
            <aside class="article-sidebar">
                <!-- Retour aux actualités -->
                <div class="sidebar-card">
                    <a href="actualites.php" class="btn btn-outline btn-full">
                        <i class="fas fa-arrow-left"></i> Retour aux actualités
                    </a>
                </div>
                
                <!-- Articles similaires -->
                <div class="sidebar-card">
                    <h3>Articles similaires</h3>
                    <div class="related-articles">
                        <?php
                        try {
                            $stmt = $pdo->prepare("SELECT id, titre, date_publication, extrait FROM actualites WHERE categorie = ? AND id != ? AND statut = 'publie' ORDER BY date_publication DESC LIMIT 3");
                            $stmt->execute([$actualite['categorie'], $actualite['id']]);
                            $related_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            if (!empty($related_articles)):
                                foreach ($related_articles as $related): ?>
                                    <article class="related-article">
                                        <h4><a href="actualite-detail.php?id=<?php echo $related['id']; ?>"><?php echo htmlspecialchars($related['titre']); ?></a></h4>
                                        <p class="related-date">
                                            <i class="fas fa-calendar"></i>
                                            <?php echo date('d F Y', strtotime($related['date_publication'])); ?>
                                        </p>
                                        <p class="related-excerpt"><?php echo htmlspecialchars(substr($related['extrait'], 0, 100)) . '...'; ?></p>
                                    </article>
                                <?php endforeach;
                            else: ?>
                                <p>Aucun article similaire trouvé.</p>
                            <?php endif;
                        } catch (PDOException $e) {
                            echo "<p>Erreur lors du chargement des articles similaires.</p>";
                        }
                        ?>
                    </div>
                </div>
                
                <!-- Contact -->
                <div class="sidebar-card">
                    <h3>Une question ?</h3>
                    <p>Contactez-nous pour plus d'informations sur nos actualités et nos formations.</p>
                    <a href="contact.php" class="btn btn-primary btn-full">
                        <i class="fas fa-envelope"></i> Nous contacter
                    </a>
                </div>
                
                <!-- Newsletter -->
                <div class="sidebar-card newsletter-card">
                    <h3><i class="fas fa-envelope"></i> Newsletter</h3>
                    <p>Recevez nos actualités directement par email.</p>
                    <form action="#" method="POST" class="newsletter-form">
                        <input type="email" name="email" placeholder="Votre email" required>
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-paper-plane"></i> S'abonner
                        </button>
                    </form>
                </div>
            </aside>
        </div>
    </div>
</section>

<!-- Articles récents -->
<section class="section" style="background: var(--light-gray);">
    <div class="container">
        <div class="section-title">
            <h2>Actualités récentes</h2>
            <p>Découvrez nos dernières actualités</p>
        </div>
        
        <div class="news-grid">
            <?php
            try {
                $stmt = $pdo->prepare("SELECT * FROM actualites WHERE id != ? AND statut = 'publie' ORDER BY date_publication DESC LIMIT 3");
                $stmt->execute([$actualite['id']]);
                $recent_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($recent_articles as $recent): ?>
                    <article class="news-card">
                        <div class="news-image">
                            <div class="news-category <?php echo htmlspecialchars($recent['categorie']); ?>">
                                <?php echo $categories[$recent['categorie']] ?? 'Actualité'; ?>
                            </div>
                            <i class="fas fa-<?php echo $icons[$recent['categorie']] ?? 'newspaper'; ?> news-icon"></i>
                        </div>
                        <div class="news-content">
                            <div class="news-meta">
                                <span class="news-date">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('d F Y', strtotime($recent['date_publication'])); ?>
                                </span>
                            </div>
                            <h3><?php echo htmlspecialchars($recent['titre']); ?></h3>
                            <p class="news-excerpt"><?php echo htmlspecialchars($recent['extrait']); ?></p>
                            <div class="news-actions">
                                <a href="actualite-detail.php?id=<?php echo $recent['id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Lire la suite
                                </a>
                            </div>
                        </div>
                    </article>
                <?php endforeach;
            } catch (PDOException $e) {
                // En cas d'erreur, on continue
            }
            ?>
        </div>
    </div>
</section>

<script>
const currentUrl = window.location.href;
const articleTitle = <?php echo json_encode($actualite['titre']); ?>;

function shareOnFacebook() {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`, '_blank');
}

function shareOnTwitter() {
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(articleTitle)}`, '_blank');
}

function shareOnLinkedIn() {
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(currentUrl)}`, '_blank');
}

function shareByEmail() {
    window.location.href = `mailto:?subject=${encodeURIComponent(articleTitle)}&body=${encodeURIComponent('Découvrez cet article : ' + currentUrl)}`;
}

function copyLink() {
    navigator.clipboard.writeText(currentUrl).then(() => {
        alert('Lien copié dans le presse-papiers !');
    });
}
</script>

<?php include 'includes/footer.php'; ?>
