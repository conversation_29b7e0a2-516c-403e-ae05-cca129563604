-- =====================================================
-- BASE DE DONNÉES COMPLÈTE POUR TOUTES LES PAGES
-- PROJET PÔLE INDUSTRIE - VERSION COMPLÈTE
-- =====================================================

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS pole_industrie CHARACTER SET utf8 COLLATE utf8_general_ci;
USE pole_industrie;

-- =====================================================
-- 1. TABLE DES UTILISATEURS ADMINISTRATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- =====================================================
-- 2. TABLE DES SECTEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS secteurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    couleur VARCHAR(7) DEFAULT '#1e3a8a',
    icone VARCHAR(50) DEFAULT 'fas fa-cog',
    ordre INT DEFAULT 0,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. TABLE DES FORMATIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duree VARCHAR(50),
    niveau ENUM('CAP', 'BTS', 'Licence Pro', 'Formation Continue', 'Formation Courte', 'Apprentissage') NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    prix DECIMAL(10,2),
    places_disponibles INT DEFAULT 0,
    date_debut DATE,
    date_fin DATE,
    lieu VARCHAR(255),
    statut ENUM('active', 'inactive', 'complete') DEFAULT 'active',
    image VARCHAR(255),
    brochure VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 4. TABLE DES ACTUALITÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    extrait TEXT,
    image VARCHAR(255),
    categorie ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') DEFAULT 'general',
    statut ENUM('brouillon', 'publie', 'archive') DEFAULT 'brouillon',
    featured BOOLEAN DEFAULT FALSE,
    date_publication DATE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 5. TABLE DES ÉVÉNEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME,
    lieu VARCHAR(255),
    type_evenement ENUM('conference', 'salon', 'portes_ouvertes', 'formation', 'autre') DEFAULT 'autre',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    places_max INT,
    places_reservees INT DEFAULT 0,
    prix DECIMAL(10,2) DEFAULT 0,
    image VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 6. TABLE DES EMPLOIS DU TEMPS
-- =====================================================
CREATE TABLE IF NOT EXISTS emplois_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    date_cours DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    salle VARCHAR(100),
    formateur VARCHAR(255),
    type_cours ENUM('cours', 'tp', 'td', 'examen', 'stage') DEFAULT 'cours',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 7. TABLE DES MESSAGES DE CONTACT
-- =====================================================
CREATE TABLE IF NOT EXISTS contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    sujet VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'thermique', 'automobile', 'navale', 'general') NULL,
    statut ENUM('nouveau', 'lu', 'traite', 'archive') DEFAULT 'nouveau',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    reponse TEXT NULL,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 8. TABLE DES INSCRIPTIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS inscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT NOT NULL,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    adresse TEXT,
    date_naissance DATE,
    niveau_etude VARCHAR(100),
    experience_professionnelle TEXT,
    motivation TEXT,
    statut ENUM('en_attente', 'accepte', 'refuse', 'liste_attente') DEFAULT 'en_attente',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    commentaires TEXT,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 9. TABLE DES PARAMÈTRES DU SITE
-- =====================================================
CREATE TABLE IF NOT EXISTS parametres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cle VARCHAR(100) UNIQUE NOT NULL,
    valeur TEXT,
    description TEXT,
    type ENUM('text', 'textarea', 'number', 'boolean', 'email', 'url') DEFAULT 'text',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 10. TABLE DES TÉMOIGNAGES
-- =====================================================
CREATE TABLE IF NOT EXISTS testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    formation VARCHAR(255),
    entreprise VARCHAR(255),
    poste VARCHAR(255),
    temoignage TEXT NOT NULL,
    note INT DEFAULT 5,
    photo VARCHAR(255),
    statut ENUM('en_attente', 'publie', 'archive') DEFAULT 'en_attente',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_publication DATE NULL
);

-- =====================================================
-- 11. TABLE DES PARTENAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS partenaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    logo VARCHAR(255),
    description TEXT,
    site_web VARCHAR(255),
    secteur VARCHAR(100),
    type_partenariat ENUM('entreprise', 'institutionnel', 'formation', 'equipement') DEFAULT 'entreprise',
    actif BOOLEAN DEFAULT TRUE,
    ordre INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 12. TABLE DES ÉQUIPEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS equipements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    salle VARCHAR(100),
    quantite INT DEFAULT 1,
    etat ENUM('excellent', 'bon', 'moyen', 'maintenance') DEFAULT 'bon',
    date_acquisition DATE,
    valeur DECIMAL(10,2),
    fournisseur VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 13. TABLE DES FORMATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS formateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    telephone VARCHAR(50),
    specialites TEXT,
    experience_annees INT,
    diplomes TEXT,
    photo VARCHAR(255),
    bio TEXT,
    secteur_principal ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general'),
    statut ENUM('actif', 'inactif', 'vacataire') DEFAULT 'actif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 14. TABLE DES PROJETS ÉTUDIANTS
-- =====================================================
CREATE TABLE IF NOT EXISTS projets_etudiants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    formation_id INT,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general'),
    etudiants TEXT,
    encadrant VARCHAR(255),
    date_debut DATE,
    date_fin DATE,
    statut ENUM('en_cours', 'termine', 'presente', 'prime') DEFAULT 'en_cours',
    image VARCHAR(255),
    fichiers TEXT,
    note DECIMAL(4,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE SET NULL
);

-- =====================================================
-- INSERTION DES DONNÉES PAR DÉFAUT
-- =====================================================

-- Utilisateurs admin
INSERT IGNORE INTO admin_users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$8K1p/wJIiU.5keowvUuYQOuiuiuqiUYqyMxdnQX9.vGA/l9dcCCO6', 'admin'),
('editor', '<EMAIL>', '$2y$10$8K1p/wJIiU.5keowvUuYQOuiuiuqiUYqyMxdnQX9.vGA/l9dcCCO6', 'editor');

-- Secteurs
INSERT IGNORE INTO secteurs (nom, slug, description, couleur, icone, ordre) VALUES
('Pôle Industrie de la Mécanique', 'mecanique', 'Formation aux métiers de la mécanique industrielle et de précision', '#1e3a8a', 'fas fa-cog', 1),
('Pôle Industrie de l\'Électricité', 'electricite', 'Expertise en systèmes électriques et électroniques industriels', '#dc2626', 'fas fa-bolt', 2),
('Pôle Génie Thermique', 'thermique', 'Spécialisation en systèmes de chauffage et climatisation', '#7c3aed', 'fas fa-thermometer-half', 3),
('Pôle Industrie de l\'Automobile', 'automobile', 'Formation aux technologies automobiles modernes', '#0ea5e9', 'fas fa-car', 4),
('Pôle Industrie Navale', 'navale', 'Métiers de la construction et réparation navale', '#0d9488', 'fas fa-ship', 5),
('Pôle Métiers de l\'Industrie', 'general', 'Formation transversale aux métiers industriels', '#16a34a', 'fas fa-tools', 6);

-- Paramètres du site
INSERT IGNORE INTO parametres (cle, valeur, description, type) VALUES
('site_name', 'Pôle Industrie', 'Nom du site', 'text'),
('site_description', 'Centre de formation et d\'excellence industrielle', 'Description du site', 'textarea'),
('contact_email', '<EMAIL>', 'Email de contact principal', 'email'),
('contact_phone', '+33 1 23 45 67 89', 'Téléphone de contact', 'text'),
('contact_address', '123 Avenue de l\'Industrie, 75000 Paris, France', 'Adresse de contact', 'textarea'),
('facebook_url', 'https://facebook.com/poleindustrie', 'URL Facebook', 'url'),
('linkedin_url', 'https://linkedin.com/company/poleindustrie', 'URL LinkedIn', 'url'),
('twitter_url', 'https://twitter.com/poleindustrie', 'URL Twitter', 'url'),
('youtube_url', 'https://youtube.com/poleindustrie', 'URL YouTube', 'url');

-- =====================================================
-- DONNÉES POUR TOUTES LES PAGES DU PROJET
-- =====================================================

-- FORMATIONS (pour pages index.php et formations.php)
INSERT IGNORE INTO formations (nom, description, duree, niveau, secteur, prix, places_disponibles, date_debut, date_fin, lieu, statut, created_by) VALUES
('CAP Maintenance des Véhicules', 'Formation complète en maintenance automobile avec pratique sur véhicules récents', '2 ans', 'CAP', 'automobile', 0.00, 20, '2024-09-01', '2026-06-30', 'Atelier Automobile', 'active', 1),
('BTS Électrotechnique', 'Formation en systèmes électriques industriels et domotique', '2 ans', 'BTS', 'electricite', 0.00, 15, '2024-09-01', '2026-06-30', 'Laboratoire Électricité', 'active', 1),
('Formation Continue Soudage', 'Perfectionnement en techniques de soudage industriel', '3 mois', 'Formation Continue', 'mecanique', 2500.00, 12, '2024-03-01', '2024-05-31', 'Atelier Mécanique', 'active', 1),
('Licence Pro Génie Thermique', 'Spécialisation en systèmes de chauffage et climatisation', '1 an', 'Licence Pro', 'thermique', 0.00, 25, '2024-09-01', '2025-06-30', 'Campus Principal', 'active', 1),
('Formation Courte Hydraulique', 'Initiation aux systèmes hydrauliques industriels', '1 mois', 'Formation Courte', 'mecanique', 1200.00, 10, '2024-02-01', '2024-02-29', 'Atelier Hydraulique', 'active', 1),
('Apprentissage Construction Navale', 'Formation en alternance aux métiers de la construction navale', '3 ans', 'Apprentissage', 'navale', 0.00, 18, '2024-09-01', '2027-06-30', 'Chantier Naval', 'active', 1),
('BTS Maintenance Industrielle', 'Formation aux techniques de maintenance préventive', '2 ans', 'BTS', 'mecanique', 0.00, 22, '2024-09-01', '2026-06-30', 'Atelier Maintenance', 'active', 1),
('CAP Réparation Carrosseries', 'Formation aux techniques de réparation automobile', '2 ans', 'CAP', 'automobile', 0.00, 16, '2024-09-01', '2026-06-30', 'Atelier Carrosserie', 'active', 1);

-- ACTUALITÉS (pour page actualites.php)
INSERT IGNORE INTO actualites (titre, contenu, extrait, categorie, statut, featured, date_publication, created_by) VALUES
('Nouvelle Formation Robotique', 'Le Pôle Industrie lance une formation en robotique industrielle...', 'Formation robotique industrielle disponible', 'mecanique', 'publie', TRUE, '2024-01-15', 1),
('Partenariat avec Renault', 'Nouveau partenariat pour la formation automobile...', 'Partenariat stratégique avec Renault', 'automobile', 'publie', TRUE, '2024-01-10', 1),
('Journée Portes Ouvertes', 'Plus de 500 visiteurs à notre journée portes ouvertes...', 'Succès de notre journée portes ouvertes', 'general', 'publie', FALSE, '2024-03-16', 1),
('Innovation Génie Thermique', 'Nos étudiants développent un système révolutionnaire...', 'Prix innovation pour nos étudiants', 'thermique', 'publie', TRUE, '2024-01-01', 1),
('Nouveau Laboratoire Électronique', 'Inauguration du laboratoire d\'électronique embarquée...', 'Nouveau laboratoire de pointe', 'electricite', 'publie', FALSE, '2024-02-20', 1),
('Salon Nautique International', 'Nos étudiants présentent leurs innovations...', 'Nos étudiants au salon nautique', 'navale', 'publie', FALSE, '2024-02-15', 1);

-- ÉVÉNEMENTS (pour admin et pages publiques)
INSERT IGNORE INTO evenements (titre, description, date_debut, date_fin, lieu, type_evenement, statut, places_max, prix, created_by) VALUES
('Journée Portes Ouvertes 2024', 'Découvrez nos formations et visitez nos ateliers', '2024-03-15 09:00:00', '2024-03-15 17:00:00', 'Campus Principal', 'portes_ouvertes', 'planifie', 200, 0.00, 1),
('Salon de l\'Industrie 4.0', 'Participation au salon national', '2024-04-10 08:00:00', '2024-04-12 18:00:00', 'Parc Expositions Paris', 'salon', 'planifie', 50, 0.00, 1),
('Conférence Automobile Électrique', 'L\'avenir de la mobilité électrique', '2024-02-20 14:00:00', '2024-02-20 17:00:00', 'Amphithéâtre A', 'conference', 'planifie', 100, 0.00, 1),
('Formation Sécurité Industrielle', 'Session obligatoire sécurité', '2024-02-05 08:00:00', '2024-02-05 12:00:00', 'Salle Formation B', 'formation', 'planifie', 30, 0.00, 1);

-- EMPLOIS DU TEMPS (pour admin/emplois-temps.php)
INSERT IGNORE INTO emplois_temps (formation_id, titre, description, date_cours, heure_debut, heure_fin, salle, formateur, type_cours, created_by) VALUES
(1, 'Diagnostic Électronique', 'Utilisation outils diagnostic OBD', '2024-02-05', '08:00:00', '12:00:00', 'Atelier Auto A', 'M. Dubois', 'tp', 1),
(1, 'Mécanique Générale', 'Cours théorique systèmes mécaniques', '2024-02-05', '14:00:00', '16:00:00', 'Salle 101', 'Mme Martin', 'cours', 1),
(2, 'Électrotechnique Industrielle', 'Étude moteurs électriques', '2024-02-06', '08:00:00', '10:00:00', 'Labo Élec 1', 'M. Leroy', 'cours', 1),
(2, 'TP Automatismes', 'Automates programmables', '2024-02-06', '10:30:00', '12:30:00', 'Labo Élec 2', 'M. Leroy', 'tp', 1),
(3, 'Soudage TIG', 'Perfectionnement TIG acier inox', '2024-02-07', '08:00:00', '12:00:00', 'Atelier Soudage', 'M. Rousseau', 'tp', 1),
(4, 'Thermodynamique', 'Calculs systèmes HVAC', '2024-02-08', '09:00:00', '11:00:00', 'Salle 205', 'Mme Petit', 'cours', 1);

-- FORMATEURS (pour enrichir les données)
INSERT IGNORE INTO formateurs (nom, prenom, email, telephone, specialites, experience_annees, secteur_principal, statut) VALUES
('Dubois', 'Jean', '<EMAIL>', '***********.89', 'Diagnostic automobile, Électronique embarquée', 15, 'automobile', 'actif'),
('Martin', 'Sophie', '<EMAIL>', '***********.90', 'Mécanique générale, Hydraulique', 12, 'mecanique', 'actif'),
('Leroy', 'Pierre', '<EMAIL>', '***********.91', 'Électrotechnique, Automatismes', 18, 'electricite', 'actif'),
('Rousseau', 'Marc', '<EMAIL>', '***********.92', 'Soudage, Chaudronnerie', 20, 'mecanique', 'actif'),
('Petit', 'Anne', '<EMAIL>', '***********.93', 'Génie thermique, Climatisation', 10, 'thermique', 'actif');

-- PARTENAIRES (pour page index.php)
INSERT IGNORE INTO partenaires (nom, description, secteur, type_partenariat, actif, ordre) VALUES
('Renault', 'Constructeur automobile français', 'Automobile', 'entreprise', TRUE, 1),
('Schneider Electric', 'Spécialiste de l\'énergie', 'Électricité', 'entreprise', TRUE, 2),
('Naval Group', 'Construction navale de défense', 'Naval', 'entreprise', TRUE, 3),
('Dassault Aviation', 'Constructeur aéronautique', 'Mécanique', 'entreprise', TRUE, 4),
('EDF', 'Producteur d\'électricité', 'Électricité', 'entreprise', TRUE, 5);

-- TÉMOIGNAGES (pour page index.php)
INSERT IGNORE INTO testimonials (nom, prenom, formation, entreprise, poste, temoignage, note, statut, date_publication) VALUES
('Dupont', 'Thomas', 'BTS Électrotechnique', 'Schneider Electric', 'Technicien Maintenance', 'Excellente formation qui m\'a permis de décrocher un emploi immédiatement après mon diplôme. Les formateurs sont très compétents.', 5, 'publie', '2024-01-01'),
('Moreau', 'Julie', 'CAP Maintenance Véhicules', 'Renault', 'Mécanicienne', 'Formation très pratique avec de vrais véhicules. J\'ai appris énormément et je recommande vivement cette école.', 5, 'publie', '2024-01-05'),
('Bernard', 'Antoine', 'Licence Pro Génie Thermique', 'Daikin', 'Ingénieur Études', 'Le niveau de formation est excellent. Les équipements sont modernes et les projets très formateurs.', 4, 'publie', '2024-01-10'),
('Roussel', 'Marie', 'Formation Continue Soudage', 'Naval Group', 'Soudeuse Qualifiée', 'Reconversion réussie grâce à cette formation de qualité. Encadrement professionnel et bienveillant.', 5, 'publie', '2024-01-15');

-- ÉQUIPEMENTS (pour pages secteurs et admin)
INSERT IGNORE INTO equipements (nom, description, secteur, salle, quantite, etat, date_acquisition, valeur) VALUES
('Robot Collaboratif UR10', 'Robot 6 axes pour formation robotique', 'mecanique', 'Atelier Robotique', 2, 'excellent', '2023-09-01', 45000.00),
('Banc Moteur Diesel', 'Banc d\'essai moteur avec acquisition données', 'automobile', 'Atelier Auto A', 1, 'bon', '2022-03-15', 25000.00),
('Automate Siemens S7-1500', 'Automate programmable industriel', 'electricite', 'Labo Élec 1', 8, 'excellent', '2023-01-10', 3500.00),
('Poste Soudage TIG', 'Poste soudage TIG/MIG professionnel', 'mecanique', 'Atelier Soudage', 6, 'bon', '2021-06-20', 4500.00),
('Pompe à Chaleur Didactique', 'Installation complète pour formation', 'thermique', 'Labo Thermique', 1, 'excellent', '2023-05-01', 15000.00);

-- PROJETS ÉTUDIANTS (pour enrichir le contenu)
INSERT IGNORE INTO projets_etudiants (titre, description, formation_id, secteur, etudiants, encadrant, date_debut, date_fin, statut, note) VALUES
('Véhicule Électrique Autonome', 'Développement d\'un prototype de véhicule électrique avec conduite autonome', 1, 'automobile', 'Thomas Dupont, Julie Martin, Pierre Leroy', 'M. Dubois', '2023-09-01', '2024-06-30', 'en_cours', NULL),
('Système Domotique IoT', 'Création d\'un système domotique connecté avec interface mobile', 2, 'electricite', 'Sophie Bernard, Antoine Roussel', 'M. Leroy', '2023-10-01', '2024-05-31', 'en_cours', NULL),
('Robot Soudeur Automatisé', 'Robot de soudage automatisé pour pièces complexes', 3, 'mecanique', 'Marie Petit, Jean Moreau', 'M. Rousseau', '2023-11-01', '2024-04-30', 'termine', 18.5),
('Pompe à Chaleur Intelligente', 'Système de chauffage intelligent avec IA', 4, 'thermique', 'Lucas Dubois, Emma Martin', 'Mme Petit', '2023-09-15', '2024-06-15', 'en_cours', NULL);

-- INSCRIPTIONS (pour admin/formations.php)
INSERT IGNORE INTO inscriptions (formation_id, nom, prenom, email, telephone, motivation, statut, date_inscription) VALUES
(1, 'Durand', 'Paul', '<EMAIL>', '***********.78', 'Passionné d\'automobile depuis toujours', 'accepte', '2024-01-15 10:30:00'),
(1, 'Lemoine', 'Sarah', '<EMAIL>', '***********.89', 'Reconversion professionnelle', 'en_attente', '2024-01-20 14:15:00'),
(2, 'Garnier', 'Maxime', '<EMAIL>', '***********.90', 'Intérêt pour les énergies renouvelables', 'accepte', '2024-01-18 09:45:00'),
(3, 'Roux', 'Camille', '<EMAIL>', '***********.01', 'Expérience en métallurgie', 'accepte', '2024-01-22 16:20:00'),
(4, 'Blanc', 'Nicolas', '<EMAIL>', '***********.12', 'Diplômé en génie mécanique', 'en_attente', '2024-01-25 11:10:00');

-- MESSAGES DE CONTACT (pour contact.php et admin)
INSERT IGNORE INTO contacts (nom, email, telephone, sujet, message, secteur, statut, date_creation) VALUES
('Martin', '<EMAIL>', '***********.89', 'Demande d\'information BTS', 'Bonjour, je souhaiterais avoir des informations sur le BTS Électrotechnique...', 'electricite', 'nouveau', '2024-01-20 10:30:00'),
('Dubois', '<EMAIL>', '***********.90', 'Inscription formation continue', 'Je suis intéressé par la formation continue en soudage...', 'mecanique', 'lu', '2024-01-18 14:15:00'),
('Leroy', '<EMAIL>', '***********.01', 'Partenariat entreprise', 'Notre entreprise souhaiterait établir un partenariat...', 'general', 'traite', '2024-01-15 09:20:00'),
('Rousseau', '<EMAIL>', '***********.12', 'Visite des installations', 'Possible de visiter vos ateliers avant inscription ?', 'automobile', 'nouveau', '2024-01-22 16:45:00');

-- DONNÉES SUPPLÉMENTAIRES POUR ADMIN
-- Mise à jour des statistiques pour le dashboard admin
UPDATE admin_users SET last_login = '2024-01-25 08:30:00' WHERE username = 'admin';
UPDATE admin_users SET last_login = '2024-01-24 14:20:00' WHERE username = 'editor';
