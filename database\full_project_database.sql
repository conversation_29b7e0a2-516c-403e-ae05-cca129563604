-- =====================================================
-- BASE DE DONNÉES COMPLÈTE POUR TOUTES LES PAGES
-- PROJET PÔLE INDUSTRIE - VERSION COMPLÈTE
-- =====================================================

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS pole_industrie CHARACTER SET utf8 COLLATE utf8_general_ci;
USE pole_industrie;

-- =====================================================
-- 1. TABLE DES UTILISATEURS ADMINISTRATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- =====================================================
-- 2. TABLE DES SECTEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS secteurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    couleur VARCHAR(7) DEFAULT '#1e3a8a',
    icone VARCHAR(50) DEFAULT 'fas fa-cog',
    ordre INT DEFAULT 0,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. TABLE DES FORMATIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duree VARCHAR(50),
    niveau ENUM('CAP', 'BTS', 'Licence Pro', 'Formation Continue', 'Formation Courte', 'Apprentissage') NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    prix DECIMAL(10,2),
    places_disponibles INT DEFAULT 0,
    date_debut DATE,
    date_fin DATE,
    lieu VARCHAR(255),
    statut ENUM('active', 'inactive', 'complete') DEFAULT 'active',
    image VARCHAR(255),
    brochure VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 4. TABLE DES ACTUALITÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    extrait TEXT,
    image VARCHAR(255),
    categorie ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') DEFAULT 'general',
    statut ENUM('brouillon', 'publie', 'archive') DEFAULT 'brouillon',
    featured BOOLEAN DEFAULT FALSE,
    date_publication DATE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 5. TABLE DES ÉVÉNEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME,
    lieu VARCHAR(255),
    type_evenement ENUM('conference', 'salon', 'portes_ouvertes', 'formation', 'autre') DEFAULT 'autre',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    places_max INT,
    places_reservees INT DEFAULT 0,
    prix DECIMAL(10,2) DEFAULT 0,
    image VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 6. TABLE DES EMPLOIS DU TEMPS
-- =====================================================
CREATE TABLE IF NOT EXISTS emplois_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    date_cours DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    salle VARCHAR(100),
    formateur VARCHAR(255),
    type_cours ENUM('cours', 'tp', 'td', 'examen', 'stage') DEFAULT 'cours',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 7. TABLE DES MESSAGES DE CONTACT
-- =====================================================
CREATE TABLE IF NOT EXISTS contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    sujet VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'thermique', 'automobile', 'navale', 'general') NULL,
    statut ENUM('nouveau', 'lu', 'traite', 'archive') DEFAULT 'nouveau',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    reponse TEXT NULL,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 8. TABLE DES INSCRIPTIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS inscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT NOT NULL,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    adresse TEXT,
    date_naissance DATE,
    niveau_etude VARCHAR(100),
    experience_professionnelle TEXT,
    motivation TEXT,
    statut ENUM('en_attente', 'accepte', 'refuse', 'liste_attente') DEFAULT 'en_attente',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    commentaires TEXT,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 9. TABLE DES PARAMÈTRES DU SITE
-- =====================================================
CREATE TABLE IF NOT EXISTS parametres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cle VARCHAR(100) UNIQUE NOT NULL,
    valeur TEXT,
    description TEXT,
    type ENUM('text', 'textarea', 'number', 'boolean', 'email', 'url') DEFAULT 'text',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 10. TABLE DES TÉMOIGNAGES
-- =====================================================
CREATE TABLE IF NOT EXISTS testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    formation VARCHAR(255),
    entreprise VARCHAR(255),
    poste VARCHAR(255),
    temoignage TEXT NOT NULL,
    note INT DEFAULT 5,
    photo VARCHAR(255),
    statut ENUM('en_attente', 'publie', 'archive') DEFAULT 'en_attente',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_publication DATE NULL
);

-- =====================================================
-- 11. TABLE DES PARTENAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS partenaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    logo VARCHAR(255),
    description TEXT,
    site_web VARCHAR(255),
    secteur VARCHAR(100),
    type_partenariat ENUM('entreprise', 'institutionnel', 'formation', 'equipement') DEFAULT 'entreprise',
    actif BOOLEAN DEFAULT TRUE,
    ordre INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 12. TABLE DES ÉQUIPEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS equipements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    salle VARCHAR(100),
    quantite INT DEFAULT 1,
    etat ENUM('excellent', 'bon', 'moyen', 'maintenance') DEFAULT 'bon',
    date_acquisition DATE,
    valeur DECIMAL(10,2),
    fournisseur VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 13. TABLE DES FORMATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS formateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    telephone VARCHAR(50),
    specialites TEXT,
    experience_annees INT,
    diplomes TEXT,
    photo VARCHAR(255),
    bio TEXT,
    secteur_principal ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general'),
    statut ENUM('actif', 'inactif', 'vacataire') DEFAULT 'actif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 14. TABLE DES PROJETS ÉTUDIANTS
-- =====================================================
CREATE TABLE IF NOT EXISTS projets_etudiants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    formation_id INT,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general'),
    etudiants TEXT,
    encadrant VARCHAR(255),
    date_debut DATE,
    date_fin DATE,
    statut ENUM('en_cours', 'termine', 'presente', 'prime') DEFAULT 'en_cours',
    image VARCHAR(255),
    fichiers TEXT,
    note DECIMAL(4,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE SET NULL
);

-- =====================================================
-- INSERTION DES DONNÉES PAR DÉFAUT
-- =====================================================

-- Utilisateurs admin
INSERT IGNORE INTO admin_users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$8K1p/wJIiU.5keowvUuYQOuiuiuqiUYqyMxdnQX9.vGA/l9dcCCO6', 'admin'),
('editor', '<EMAIL>', '$2y$10$8K1p/wJIiU.5keowvUuYQOuiuiuqiUYqyMxdnQX9.vGA/l9dcCCO6', 'editor');

-- Secteurs
INSERT IGNORE INTO secteurs (nom, slug, description, couleur, icone, ordre) VALUES
('Pôle Industrie de la Mécanique', 'mecanique', 'Formation aux métiers de la mécanique industrielle et de précision', '#1e3a8a', 'fas fa-cog', 1),
('Pôle Industrie de l\'Électricité', 'electricite', 'Expertise en systèmes électriques et électroniques industriels', '#dc2626', 'fas fa-bolt', 2),
('Pôle Génie Thermique', 'thermique', 'Spécialisation en systèmes de chauffage et climatisation', '#7c3aed', 'fas fa-thermometer-half', 3),
('Pôle Industrie de l\'Automobile', 'automobile', 'Formation aux technologies automobiles modernes', '#0ea5e9', 'fas fa-car', 4),
('Pôle Industrie Navale', 'navale', 'Métiers de la construction et réparation navale', '#0d9488', 'fas fa-ship', 5),
('Pôle Métiers de l\'Industrie', 'general', 'Formation transversale aux métiers industriels', '#16a34a', 'fas fa-tools', 6);

-- Paramètres du site
INSERT IGNORE INTO parametres (cle, valeur, description, type) VALUES
('site_name', 'Pôle Industrie', 'Nom du site', 'text'),
('site_description', 'Centre de formation et d\'excellence industrielle', 'Description du site', 'textarea'),
('contact_email', '<EMAIL>', 'Email de contact principal', 'email'),
('contact_phone', '+33 1 23 45 67 89', 'Téléphone de contact', 'text'),
('contact_address', '123 Avenue de l\'Industrie, 75000 Paris, France', 'Adresse de contact', 'textarea'),
('facebook_url', 'https://facebook.com/poleindustrie', 'URL Facebook', 'url'),
('linkedin_url', 'https://linkedin.com/company/poleindustrie', 'URL LinkedIn', 'url'),
('twitter_url', 'https://twitter.com/poleindustrie', 'URL Twitter', 'url'),
('youtube_url', 'https://youtube.com/poleindustrie', 'URL YouTube', 'url');

-- =====================================================
-- DONNÉES POUR TOUTES LES PAGES DU PROJET
-- =====================================================

-- FORMATIONS COMPLÈTES (pour pages index.php et formations.php)
INSERT IGNORE INTO formations (nom, description, duree, niveau, secteur, prix, places_disponibles, date_debut, date_fin, lieu, statut, created_by) VALUES
('CAP Maintenance des Véhicules Automobiles', 'Formation complète en maintenance automobile avec pratique sur véhicules récents. Apprentissage des techniques de diagnostic électronique, mécanique moteur, systèmes de freinage, climatisation et électricité automobile. Formation incluant les nouvelles technologies hybrides et électriques. Stage en entreprise de 12 semaines. Débouchés garantis dans les concessions et garages partenaires.', '2 ans', 'CAP', 'automobile', 0.00, 20, '2024-09-01', '2026-06-30', 'Atelier Automobile - Bâtiment A', 'active', 1),

('BTS Électrotechnique et Systèmes Industriels', 'Formation approfondie en systèmes électriques industriels, domotique et énergies renouvelables. Étude des automatismes, variateurs de vitesse, réseaux industriels, supervision SCADA. Projets sur installations réelles. Formation aux normes de sécurité électrique. Partenariat avec Schneider Electric pour les équipements. Stage de 10 semaines en entreprise industrielle.', '2 ans', 'BTS', 'electricite', 0.00, 15, '2024-09-01', '2026-06-30', 'Laboratoire Électricité - Bâtiment B', 'active', 1),

('Formation Continue Soudage TIG/MIG/MAG', 'Perfectionnement en techniques de soudage industriel sur tous matériaux : acier, inox, aluminium. Apprentissage des procédés TIG, MIG/MAG, soudage orbital. Qualification selon normes EN ISO 9606. Formation aux contrôles non destructifs. Matériel professionnel Lincoln Electric. Certification internationale. Placement garanti dans l\'industrie navale et pétrochimique.', '3 mois', 'Formation Continue', 'mecanique', 2500.00, 12, '2024-03-01', '2024-05-31', 'Atelier Soudage - Bâtiment C', 'active', 1),

('Licence Professionnelle Génie Thermique et Énergies', 'Spécialisation en systèmes de chauffage, climatisation, énergies renouvelables et efficacité énergétique. Étude des pompes à chaleur, systèmes solaires, géothermie. Dimensionnement d\'installations, audit énergétique, réglementation thermique RT2020. Logiciels professionnels : Climawin, Perrenoud. Projet de fin d\'études en entreprise. Débouchés : bureau d\'études, installateur, auditeur énergétique.', '1 an', 'Licence Pro', 'thermique', 0.00, 25, '2024-09-01', '2025-06-30', 'Campus Principal - Laboratoire Thermique', 'active', 1),

('Formation Courte Hydraulique Industrielle et Mobile', 'Initiation complète aux systèmes hydrauliques industriels et mobiles. Étude des composants : pompes, vérins, distributeurs, accumulateurs. Lecture de schémas hydrauliques, maintenance préventive, diagnostic de pannes. Travaux pratiques sur bancs didactiques Bosch Rexroth. Habilitation hydraulique. Formation très demandée dans l\'industrie et les travaux publics.', '1 mois', 'Formation Courte', 'mecanique', 1200.00, 10, '2024-02-01', '2024-02-29', 'Atelier Hydraulique - Bâtiment D', 'active', 1),

('Apprentissage Construction et Réparation Navale', 'Formation complète en alternance aux métiers de la construction navale : chaudronnerie, soudage, assemblage de coques. Apprentissage sur chantiers navals réels. Techniques de découpe plasma, cintrage, rivetage. Lecture de plans navals, contrôle qualité. Partenariat avec Naval Group et chantiers de l\'Atlantique. Emploi garanti à l\'issue de la formation.', '3 ans', 'Apprentissage', 'navale', 0.00, 18, '2024-09-01', '2027-06-30', 'Chantier Naval Partenaire - Saint-Nazaire', 'active', 1),

('BTS Maintenance des Systèmes Industriels', 'Formation complète aux techniques de maintenance préventive et corrective des équipements industriels. Étude des machines-outils, robots, convoyeurs, systèmes automatisés. GMAO, planification, gestion des stocks. Maintenance électrique, mécanique, pneumatique. Habilitations électriques B1V, B2V. Stage de 8 semaines. Débouchés dans toutes les industries.', '2 ans', 'BTS', 'mecanique', 0.00, 22, '2024-09-01', '2026-06-30', 'Atelier Maintenance - Bâtiment E', 'active', 1),

('CAP Réparation des Carrosseries Automobiles', 'Formation spécialisée en réparation et peinture automobile. Techniques de débosselage, ponçage, masticage, préparation des surfaces. Peinture au pistolet, cabine de peinture professionnelle. Réparation des plastiques, pare-brise. Colorimétrie, retouches. Matériel Sikkens et PPG. Stage en carrosserie agréée assurance. Emploi garanti dans le réseau partenaire.', '2 ans', 'CAP', 'automobile', 0.00, 16, '2024-09-01', '2026-06-30', 'Atelier Carrosserie - Bâtiment F', 'active', 1),

-- NOUVELLES FORMATIONS AJOUTÉES
('BTS Systèmes Numériques Option Électronique', 'Formation complète aux systèmes électroniques embarqués et aux réseaux de communication. Étude des microcontrôleurs, FPGA, systèmes temps réel, protocoles de communication (CAN, LIN, Ethernet). Programmation C/C++, Python, développement d\'applications mobiles IoT. Projets sur cartes Arduino, Raspberry Pi, STM32. Stage de 10 semaines en entreprise high-tech. Débouchés : développeur embarqué, technicien R&D, intégrateur systèmes.', '2 ans', 'BTS', 'electricite', 0.00, 18, '2024-09-01', '2026-06-30', 'Laboratoire Électronique - Bâtiment G', 'active', 1),

('Formation Continue Usinage CNC 5 Axes', 'Perfectionnement en usinage sur machines-outils à commande numérique 5 axes. Programmation avancée, stratégies d\'usinage complexes, pièces aéronautiques et médicales. Logiciels CAO/FAO : SolidWorks, Mastercam, PowerMill. Machines Haas, DMG Mori, Mazak. Métrologie 3D, contrôle qualité. Formation très demandée dans l\'aéronautique, le médical, l\'automobile de luxe. Certification constructeur incluse.', '4 mois', 'Formation Continue', 'mecanique', 4200.00, 8, '2024-03-01', '2024-06-30', 'Atelier Usinage CNC - Bâtiment H', 'active', 1),

('Licence Pro Énergies Renouvelables et Efficacité Énergétique', 'Formation spécialisée dans les technologies vertes : photovoltaïque, éolien, biomasse, géothermie. Dimensionnement d\'installations, étude de faisabilité, réglementation environnementale. Audit énergétique, certification HQE, RT2020. Logiciels : PVSyst, Homer, Pleiades. Projets avec EDF Renouvelables, Engie. Stage de 16 semaines. Débouchés : chargé d\'affaires énergies renouvelables, auditeur énergétique, chef de projet développement durable.', '1 an', 'Licence Pro', 'thermique', 0.00, 20, '2024-09-01', '2025-06-30', 'Campus Développement Durable', 'active', 1),

('CAP Métallerie Ferronnerie', 'Formation aux techniques traditionnelles et modernes de la métallerie. Forge, soudage, découpe plasma, cintrage, assemblage. Réalisation de portails, rampes, structures métalliques. Lecture de plans, traçage, métrologie. Sécurité en hauteur, habilitations. Matériel Lincoln Electric, Kemper. Partenariat avec les Compagnons du Devoir. Stage en entreprise artisanale. Débouchés : métallier, ferronnier d\'art, serrurier.', '2 ans', 'CAP', 'mecanique', 0.00, 14, '2024-09-01', '2026-06-30', 'Atelier Métallerie - Bâtiment I', 'active', 1),

('BTS Conception et Réalisation de Systèmes Automatiques', 'Formation aux systèmes automatisés industriels : robots, convoyeurs, machines spéciales. Conception mécanique, programmation automates, supervision HMI. Logiciels : SolidWorks, TIA Portal, Unity Pro. Réseaux industriels Profinet, Ethernet/IP. Sécurité fonctionnelle, normes CE. Projets avec Schneider Electric, Siemens, Rockwell. Stage de 8 semaines. Débouchés : automaticien, intégrateur, technicien maintenance.', '2 ans', 'BTS', 'electricite', 0.00, 16, '2024-09-01', '2026-06-30', 'Laboratoire Automatismes - Bâtiment J', 'active', 1),

('Formation Courte Impression 3D Industrielle', 'Initiation aux technologies d\'impression 3D pour l\'industrie : FDM, SLA, SLS, métal. Conception pour l\'impression 3D, supports, post-traitement. Logiciels : Fusion 360, Cura, PreForm. Matériaux : PLA, ABS, PETG, résines, métaux. Applications : prototypage, outillage, pièces de série. Machines Ultimaker, Formlabs, EOS. Formation très innovante pour l\'industrie 4.0.', '6 semaines', 'Formation Courte', 'mecanique', 1800.00, 12, '2024-04-01', '2024-05-15', 'FabLab Industriel - Bâtiment K', 'active', 1),

('Apprentissage Technicien Supérieur Naval', 'Formation d\'excellence en construction navale militaire et civile. Chaudronnerie lourde, soudage sous-marin, assemblage de coques, systèmes propulsifs. Lecture de plans navals, calculs de structure, contrôle qualité. Normes maritimes internationales, certification Lloyd\'s. Partenariat exclusif avec Naval Group, Chantiers de l\'Atlantique. Formation sur vrais navires en construction. Emploi garanti à 100%.', '3 ans', 'Apprentissage', 'navale', 0.00, 15, '2024-09-01', '2027-06-30', 'Chantier Naval - Saint-Nazaire', 'active', 1),

('BTS Fluides Énergies Domotique Option Génie Climatique', 'Formation spécialisée en climatisation, ventilation, chauffage intelligent. Systèmes VRV, CTA, pompes à chaleur, géothermie. Régulation, GTB, domotique KNX. Dimensionnement, installation, maintenance. Logiciels : Climawin, Perrenoud, HAP. Réglementation RT2020, DPE. Stage chez Daikin, Mitsubishi, Atlantic. Débouchés : technicien bureau d\'études, installateur, mainteneur systèmes climatiques.', '2 ans', 'BTS', 'thermique', 0.00, 22, '2024-09-01', '2026-06-30', 'Laboratoire Génie Climatique', 'active', 1),

('Formation Continue Conduite de Ligne Automatisée', 'Formation à la conduite et supervision de lignes de production automatisées. Pilotage d\'automates, supervision SCADA, maintenance de niveau 1. Qualité, traçabilité, amélioration continue. Sécurité industrielle, consignation. Logiciels : WinCC, Vijeo Citect, Factory Talk. Applications tous secteurs : automobile, agroalimentaire, pharmaceutique. Formation très demandée par les industriels.', '3 mois', 'Formation Continue', 'general', 2800.00, 16, '2024-02-01', '2024-04-30', 'Ligne Pilote Industrie 4.0', 'active', 1),

('Licence Pro Maintenance et Technologie Systèmes Pluritechniques', 'Formation avancée en maintenance industrielle préventive et prédictive. Analyse vibratoire, thermographie, ultrasons, analyse d\'huile. GMAO, planification, gestion des stocks. Management d\'équipe, amélioration continue. Habilitations électriques H0V-B0V-BR. Stage de 14 semaines en grande industrie. Débouchés : responsable maintenance, ingénieur méthodes, consultant maintenance.', '1 an', 'Licence Pro', 'mecanique', 0.00, 18, '2024-09-01', '2025-06-30', 'Centre de Maintenance Avancée', 'active', 1),

('CAP Électricien Bâtiment et Tertiaire', 'Formation complète en électricité du bâtiment : installations domestiques, tertiaires, industrielles légères. Câblage, tableaux électriques, éclairage, domotique. Normes NF C 15-100, habilitations électriques. Énergies renouvelables : photovoltaïque, bornes de recharge véhicules électriques. Matériel Legrand, Schneider Electric. Stage en entreprise d\'électricité. Emploi garanti dans le BTP.', '2 ans', 'CAP', 'electricite', 0.00, 20, '2024-09-01', '2026-06-30', 'Atelier Électricité Bâtiment', 'active', 1);

-- ACTUALITÉS COMPLÈTES (pour page actualites.php)
INSERT IGNORE INTO actualites (titre, contenu, extrait, categorie, statut, featured, date_publication, created_by) VALUES
('Nouvelle Formation en Robotique Industrielle 4.0', 'Le Pôle Industrie est fier d\'annoncer le lancement de sa nouvelle formation en robotique industrielle, une réponse directe aux besoins croissants de l\'industrie 4.0. Cette formation de 6 mois, unique en France, permettra aux participants de maîtriser les technologies robotiques les plus avancées.\n\nAu programme : programmation de robots collaboratifs (cobots), intelligence artificielle appliquée à la robotique, vision industrielle, maintenance prédictive et intégration dans les chaînes de production. Les étudiants travailleront sur des robots Universal Robots, KUKA et ABB dernière génération.\n\nLa formation comprend 60% de pratique en atelier équipé de 8 cellules robotisées, 30% de théorie et 10% de projet industriel réel en partenariat avec nos entreprises partenaires. Les débouchés sont excellents : technicien robotique, programmeur, intégrateur systèmes.\n\nInscriptions ouvertes dès maintenant. Prérequis : BAC+2 technique ou expérience industrielle. Financement possible via CPF, OPCO ou Pôle Emploi.', 'Découvrez notre nouvelle formation en robotique industrielle 4.0, une opportunité unique de se former aux technologies de demain avec des équipements de pointe.', 'mecanique', 'publie', TRUE, '2024-01-15', 1),

('Partenariat Stratégique Historique avec Renault', 'Le Pôle Industrie et Renault signent un partenariat stratégique majeur qui révolutionne la formation automobile en France. Cet accord, d\'une durée de 5 ans, positionne notre établissement comme centre de formation d\'excellence Renault.\n\nConcrètement, ce partenariat apporte : 15 véhicules Renault dernière génération (Clio 5, Captur, Mégane E-Tech), un simulateur de conduite professionnel, des outils de diagnostic Renault Clip, la formation de nos formateurs aux technologies Renault.\n\nLes étudiants bénéficieront de stages garantis dans le réseau Renault (200 concessions partenaires), d\'un accès privilégié aux formations constructeur, de certifications Renault reconnues et d\'un emploi prioritaire à l\'issue de leur formation.\n\nRenault s\'engage également à recruter 50 diplômés par an et à financer 10 bourses d\'excellence. Ce partenariat renforce notre position de leader dans la formation automobile et garantit l\'employabilité de nos étudiants.\n\nLes formations concernées : CAP Maintenance Véhicules, CAP Carrosserie, BTS Après-Vente Automobile.', 'Un partenariat stratégique historique avec Renault qui garantit l\'employabilité de nos étudiants et renforce notre excellence en formation automobile.', 'automobile', 'publie', TRUE, '2024-01-10', 1),

('Journée Portes Ouvertes 2024 : Un Succès Retentissant', 'La journée portes ouvertes du 15 mars 2024 restera dans les annales du Pôle Industrie ! Plus de 800 visiteurs ont découvert nos formations, soit une augmentation de 60% par rapport à l\'année précédente.\n\nLe programme riche et varié a séduit toutes les générations : démonstrations de soudage robotisé, conduite de simulateurs automobiles, manipulation de robots collaboratifs, expériences en laboratoire d\'électrotechnique. Les ateliers \"métiers\" ont permis aux visiteurs de s\'essayer aux gestes professionnels.\n\nPoints forts de la journée : 150 familles ont visité nos internats rénovés, 200 personnes ont assisté aux conférences métiers, 80 rendez-vous individuels ont été pris avec nos conseillers en formation. Les témoignages d\'anciens étudiants, aujourd\'hui en poste chez nos partenaires, ont particulièrement marqué les esprits.\n\nRésultats concrets : 120 pré-inscriptions enregistrées, 45 dossiers complets déposés le jour même. Les formations les plus demandées : BTS Électrotechnique, CAP Maintenance Automobile et Formation Continue Soudage.\n\nProchaine journée portes ouvertes : samedi 15 juin 2024. Réservez dès maintenant !', 'Retour sur notre journée portes ouvertes exceptionnelle qui a attiré plus de 800 visiteurs passionnés par nos formations industrielles d\'excellence.', 'general', 'publie', FALSE, '2024-03-16', 1),

('Prix de l\'Innovation Énergétique pour nos Étudiants', 'Nos étudiants de Licence Pro Génie Thermique remportent le 1er Prix de l\'Innovation Énergétique 2024 avec leur projet révolutionnaire \"Smart Heat Pump IA\".\n\nLeur innovation : une pompe à chaleur intelligente qui utilise l\'intelligence artificielle pour optimiser automatiquement ses performances selon les conditions météorologiques, l\'occupation du bâtiment et les tarifs énergétiques. Résultat : 35% d\'économies d\'énergie supplémentaires !\n\nL\'équipe composée de Marie Dubois, Thomas Leroy et Sarah Martin, encadrée par Mme Petit, a développé un algorithme d\'apprentissage automatique qui analyse en temps réel 50 paramètres différents. Le système s\'adapte aux habitudes des occupants et anticipe leurs besoins.\n\nLe prototype, testé sur notre campus, a démontré son efficacité : réduction de 30% de la consommation électrique, amélioration du confort thermique, maintenance prédictive intégrée. Le jury, composé d\'experts d\'EDF, Daikin et de l\'ADEME, a salué \"une innovation majeure pour la transition énergétique\".\n\nCe projet sera présenté au salon Interclima+Elec 2024 et fait l\'objet d\'un dépôt de brevet. Plusieurs industriels ont déjà manifesté leur intérêt pour une commercialisation.', 'Un projet étudiant révolutionnaire en génie thermique remporte le prix de l\'innovation énergétique 2024 avec une pompe à chaleur intelligente.', 'thermique', 'publie', TRUE, '2024-01-01', 1),

('Inauguration du Laboratoire d\'Électronique Embarquée', 'Le Pôle Industrie inaugure officiellement son nouveau laboratoire d\'électronique embarquée de 300 m², un investissement de 500 000€ qui positionne l\'établissement à la pointe de la formation automobile.\n\nCe laboratoire ultramoderne comprend : 12 postes de diagnostic équipés des derniers outils constructeurs (Bosch, Continental, Valeo), 6 véhicules école avec systèmes électroniques complets, un banc moteur avec acquisition de données en temps réel, des simulateurs de défauts électroniques.\n\nLes équipements phares : oscilloscopes Fluke dernière génération, analyseur de bus CAN/LIN, banc de test calculateurs, station de reprogrammation ECU. Les étudiants travailleront sur les technologies les plus récentes : systèmes ADAS, véhicules hybrides, bornes de recharge.\n\nCette inauguration s\'est déroulée en présence de M. Dupont, Directeur Régional de Bosch, Mme Martin, représentante du Conseil Régional, et de nos partenaires industriels. \"Ce laboratoire forme les techniciens dont nous avons besoin pour l\'automobile de demain\", a déclaré M. Dupont.\n\nLes formations bénéficiaires : CAP Maintenance Automobile, BTS Après-Vente, Formation Continue Diagnostic Électronique. Ouverture aux entreprises pour formations sur mesure.', 'Inauguration de notre nouveau laboratoire d\'électronique embarquée de pointe pour former aux technologies automobiles de demain.', 'electricite', 'publie', FALSE, '2024-02-20', 1),

('Nos Étudiants Brillent au Salon Nautique International', 'Les étudiants de la section Construction Navale du Pôle Industrie ont fait sensation au Salon Nautique International de Paris avec leurs innovations en propulsion écologique et matériaux composites.\n\nLeur projet vedette : un système de propulsion hybride pour bateaux de plaisance combinant moteur électrique, éolienne et panneaux solaires. Cette innovation permet une autonomie de 8 heures en mode 100% électrique et une réduction de 70% des émissions CO2.\n\nSeconde innovation remarquée : un nouveau composite bio-sourcé à base de fibres de lin pour la construction de coques. Ce matériau, 30% plus léger que la fibre de verre traditionnelle, offre des propriétés mécaniques équivalentes tout en étant 100% recyclable.\n\nL\'équipe de 8 étudiants, menée par Antoine Rousseau et Julie Moreau, a présenté leurs travaux devant 200 professionnels du nautisme. Plusieurs chantiers navals ont manifesté leur intérêt pour ces innovations, notamment les Chantiers Bénéteau et Jeanneau.\n\nRésultats concrets : 3 offres de stage, 2 propositions d\'embauche, 1 projet de collaboration industrielle. Le jury professionnel a décerné le Prix de l\'Innovation Jeune au projet de propulsion hybride.\n\nCes succès confirment l\'excellence de notre formation navale et l\'employabilité de nos diplômés dans ce secteur d\'avenir.', 'Nos étudiants en construction navale brillent au salon nautique avec leurs innovations en propulsion écologique et matériaux composites.', 'navale', 'publie', FALSE, '2024-02-15', 1),

-- NOUVELLES ACTUALITÉS AJOUTÉES
('Lancement du Campus Industrie 4.0 : Une Révolution Pédagogique', 'Le Pôle Industrie inaugure son nouveau Campus Industrie 4.0, un investissement de 2 millions d\'euros qui positionne l\'établissement comme pionnier de la formation industrielle digitale en France.\n\nCe campus révolutionnaire comprend : une usine 4.0 miniature avec robots collaboratifs, lignes de production connectées, systèmes de vision industrielle et intelligence artificielle. Les étudiants travailleront sur des cas réels d\'optimisation de production, de maintenance prédictive et de qualité 4.0.\n\nLes équipements phares : 12 robots Universal Robots, 8 stations d\'usinage CNC connectées, système MES (Manufacturing Execution System), réalité virtuelle et augmentée pour la formation. Partenariats avec Siemens, Schneider Electric, Dassault Systèmes.\n\nCette inauguration s\'est déroulée en présence de Mme la Ministre de l\'Enseignement Professionnel, du Président de la Région et de 50 dirigeants d\'entreprises industrielles. \"Ce campus forme les techniciens de l\'industrie de demain\", a déclaré la Ministre.\n\nFormations concernées : BTS Systèmes Numériques, BTS CRSA, Licence Pro Maintenance, Formation Continue Industrie 4.0. Ouverture aux entreprises pour formations sur mesure et accompagnement transformation digitale.\n\nObjectif : former 500 techniciens par an aux métiers de l\'industrie 4.0 et accompagner 100 entreprises dans leur transformation numérique.', 'Inauguration de notre Campus Industrie 4.0 révolutionnaire qui forme aux métiers industriels de demain avec des technologies de pointe.', 'general', 'publie', TRUE, '2024-01-25', 1),

('Accord Historique avec Tesla pour la Formation Véhicules Électriques', 'Le Pôle Industrie signe un partenariat exclusif avec Tesla France pour devenir le premier centre de formation agréé Tesla en région. Cet accord révolutionnaire transforme notre approche de la formation automobile électrique.\n\nConcrètement, Tesla fournit : 3 véhicules Model 3 et Model Y pour la formation, 1 station de recharge Supercharger dédiée, outils de diagnostic Tesla exclusifs, formation de nos formateurs au Tesla Service Center de Chambourcy.\n\nLes étudiants bénéficieront de : stages prioritaires dans le réseau Tesla (15 centres en France), certification Tesla officielle, accès aux formations constructeur, emploi garanti dans l\'expansion du réseau Tesla.\n\nTesla s\'engage à recruter 20 diplômés par an et à financer 5 bourses d\'excellence \"Tesla Future Technicians\". Ce partenariat inclut aussi la recherche sur les batteries, la conduite autonome et les systèmes de recharge rapide.\n\nM. Elon Musk a personnellement validé ce partenariat : \"Le Pôle Industrie partage notre vision d\'accélérer la transition vers l\'énergie durable. Ensemble, nous formons les techniciens qui répareront les Tesla de demain.\"\n\nFormations concernées : CAP Maintenance Véhicules Électriques, BTS Après-Vente Automobile option VE, Formation Continue Diagnostic Tesla. Ouverture d\'une nouvelle section \"Technicien Tesla\" dès septembre 2024.', 'Partenariat exclusif historique avec Tesla pour former aux véhicules électriques et garantir l\'emploi de nos diplômés.', 'automobile', 'publie', TRUE, '2024-01-20', 1),

('Prix International d\'Excellence Pédagogique pour notre Innovation', 'Le Pôle Industrie remporte le Prix International d\'Excellence Pédagogique 2024 décerné par l\'UNESCO pour son programme innovant \"Apprentissage par Projet Industriel Réel\" (APIR).\n\nCette méthode révolutionnaire, développée par nos équipes pédagogiques, immerge les étudiants dans de vrais projets industriels dès la première année. Résultat : 95% de réussite aux examens et 98% d\'insertion professionnelle.\n\nLe programme APIR fonctionne ainsi : chaque promotion travaille sur 3 projets réels d\'entreprises partenaires, encadrée par des formateurs et des professionnels. Les étudiants vivent toutes les étapes : cahier des charges, conception, réalisation, tests, livraison.\n\nExemples de projets 2023 : robot de désinfection pour hôpitaux (partenariat CHU), système de tri automatique (Amazon), banc de test moteurs électriques (Valeo). Ces projets ont généré 15 brevets et 3 créations d\'entreprises par d\'anciens étudiants.\n\nLe jury international, composé d\'experts de 12 pays, a salué \"une approche pédagogique révolutionnaire qui réconcilie théorie et pratique, formation et innovation\". Ce prix s\'accompagne d\'une dotation de 100 000€ pour développer le programme.\n\nCette reconnaissance internationale confirme notre position de leader européen en formation industrielle innovante. 8 établissements étrangers souhaitent déjà adopter notre méthode APIR.', 'Notre méthode pédagogique révolutionnaire récompensée par le Prix International d\'Excellence UNESCO 2024.', 'general', 'publie', TRUE, '2024-01-18', 1),

('Ouverture du Centre d\'Excellence en Soudage Spatial', 'Le Pôle Industrie inaugure le premier Centre d\'Excellence en Soudage Spatial d\'Europe, en partenariat avec l\'ESA (Agence Spatiale Européenne) et Ariane Group. Cette installation unique forme aux techniques de soudage pour l\'industrie spatiale.\n\nCe centre ultramoderne comprend : chambre à vide pour simulation spatiale, robots de soudage orbitaux, bancs de test en apesanteur simulée, microscopes électroniques pour contrôle qualité. Les étudiants travailleront sur de vraies pièces de lanceurs Ariane 6.\n\nLes techniques enseignées : soudage TIG orbital, faisceau d\'électrons, laser, friction-malaxage. Matériaux spatiaux : alliages titane, inconel, composites carbone. Normes spatiales européennes et américaines (NASA).\n\nL\'inauguration s\'est déroulée en présence de Thomas Pesquet, astronaute ESA, qui a déclaré : \"Ces futurs soudeurs construiront les vaisseaux qui nous emmèneront sur Mars. Leur formation est cruciale pour l\'avenir de l\'exploration spatiale.\"\n\nFormation proposée : Certificat de Spécialisation \"Soudeur Spatial\" (6 mois), ouvert aux titulaires d\'un CAP soudage. 12 places par promotion, sélection sur dossier et tests pratiques. Débouchés garantis chez Ariane Group, Thales Alenia Space, Airbus Defence.\n\nCe centre positionne la France comme leader mondial de la formation spatiale et répond à la forte demande de l\'industrie spatiale européenne en pleine expansion.', 'Inauguration du premier Centre d\'Excellence en Soudage Spatial d\'Europe en partenariat avec l\'ESA et Ariane Group.', 'mecanique', 'publie', FALSE, '2024-01-12', 1),

('Révolution Énergétique : Notre Campus 100% Autonome', 'Le Pôle Industrie devient le premier établissement de formation en France à atteindre l\'autonomie énergétique complète grâce à un projet innovant mené par nos étudiants en Génie Thermique.\n\nLe projet \"Campus Vert\" combine : 2000 m² de panneaux photovoltaïques sur toitures, 3 éoliennes verticales de nouvelle génération, système de géothermie sur sondes, récupération d\'eau de pluie, méthanisation des déchets organiques.\n\nRésultats spectaculaires : 100% d\'autonomie électrique, 80% d\'autonomie en chauffage, réduction de 90% des émissions CO2, économies de 150 000€ par an. Le surplus d\'électricité est revendu à EDF, générant 30 000€ de revenus annuels.\n\nCe projet, dirigé par nos étudiants sous supervision de Mme Petit, professeure en Génie Thermique, a nécessité 18 mois de conception et réalisation. Il sert maintenant de support pédagogique pour toutes nos formations énergétiques.\n\nReconnaissance nationale : visite du Ministre de la Transition Énergétique, label \"Campus Exemplaire\" décerné par l\'ADEME, prix \"Innovation Verte\" du Salon des Maires. 50 établissements français souhaitent reproduire notre modèle.\n\nImpact pédagogique : nos étudiants maîtrisent maintenant les technologies vertes sur des installations réelles. Taux d\'insertion professionnelle de 100% dans les énergies renouvelables. Création de 2 start-ups par d\'anciens étudiants.\n\nObjectif 2025 : partager notre expertise avec 20 établissements partenaires et former 200 techniciens par an aux métiers de la transition énergétique.', 'Notre campus devient 100% autonome en énergie grâce à un projet révolutionnaire mené par nos étudiants en Génie Thermique.', 'thermique', 'publie', FALSE, '2024-01-08', 1),

('Partenariat Stratégique avec Naval Group pour la Défense', 'Le Pôle Industrie signe un accord de partenariat stratégique avec Naval Group pour former aux métiers de la construction navale de défense. Ce partenariat renforce notre position de leader en formation navale militaire.\n\nNaval Group investit 1,5 million d\'euros dans nos équipements : simulateur de soudage sous-marin, bassin d\'essais hydrodynamiques, matériaux composites militaires, systèmes de propulsion nucléaire (simulation). Formation de nos formateurs aux technologies classifiées.\n\nLes étudiants bénéficieront de : stages sur vrais sous-marins et frégates, habilitations défense, emploi prioritaire sur les programmes SNLE 3G et FDI, salaires majorés de 20% par rapport au civil.\n\nNaval Group s\'engage à recruter 30 diplômés par an sur 5 ans et à financer 10 bourses \"Défense Nationale\" pour étudiants méritants. Formation continue pour 100 salariés Naval Group par an dans nos locaux.\n\nM. Pierre-Éric Pommellet, PDG de Naval Group : \"Ce partenariat sécurise nos besoins en compétences pour les 20 prochaines années. Le Pôle Industrie forme l\'élite de la construction navale française.\"\n\nFormations concernées : Apprentissage Construction Navale Militaire (nouveau), BTS Conception Navale, Formation Continue Soudage Naval. Ouverture d\'une section \"Technicien Supérieur Naval Défense\" avec habilitation secret défense.\n\nCet accord positionne nos diplômés au cœur de la souveraineté industrielle française et européenne en matière de défense navale.', 'Accord stratégique avec Naval Group pour former l\'élite de la construction navale de défense française.', 'navale', 'publie', FALSE, '2024-01-05', 1);

-- ÉVÉNEMENTS (pour admin et pages publiques)
INSERT IGNORE INTO evenements (titre, description, date_debut, date_fin, lieu, type_evenement, statut, places_max, prix, created_by) VALUES
('Journée Portes Ouvertes 2024', 'Découvrez nos formations et visitez nos ateliers', '2024-03-15 09:00:00', '2024-03-15 17:00:00', 'Campus Principal', 'portes_ouvertes', 'planifie', 200, 0.00, 1),
('Salon de l\'Industrie 4.0', 'Participation au salon national', '2024-04-10 08:00:00', '2024-04-12 18:00:00', 'Parc Expositions Paris', 'salon', 'planifie', 50, 0.00, 1),
('Conférence Automobile Électrique', 'L\'avenir de la mobilité électrique', '2024-02-20 14:00:00', '2024-02-20 17:00:00', 'Amphithéâtre A', 'conference', 'planifie', 100, 0.00, 1),
('Formation Sécurité Industrielle', 'Session obligatoire sécurité', '2024-02-05 08:00:00', '2024-02-05 12:00:00', 'Salle Formation B', 'formation', 'planifie', 30, 0.00, 1);

-- EMPLOIS DU TEMPS (pour admin/emplois-temps.php)
INSERT IGNORE INTO emplois_temps (formation_id, titre, description, date_cours, heure_debut, heure_fin, salle, formateur, type_cours, created_by) VALUES
(1, 'Diagnostic Électronique', 'Utilisation outils diagnostic OBD', '2024-02-05', '08:00:00', '12:00:00', 'Atelier Auto A', 'M. Dubois', 'tp', 1),
(1, 'Mécanique Générale', 'Cours théorique systèmes mécaniques', '2024-02-05', '14:00:00', '16:00:00', 'Salle 101', 'Mme Martin', 'cours', 1),
(2, 'Électrotechnique Industrielle', 'Étude moteurs électriques', '2024-02-06', '08:00:00', '10:00:00', 'Labo Élec 1', 'M. Leroy', 'cours', 1),
(2, 'TP Automatismes', 'Automates programmables', '2024-02-06', '10:30:00', '12:30:00', 'Labo Élec 2', 'M. Leroy', 'tp', 1),
(3, 'Soudage TIG', 'Perfectionnement TIG acier inox', '2024-02-07', '08:00:00', '12:00:00', 'Atelier Soudage', 'M. Rousseau', 'tp', 1),
(4, 'Thermodynamique', 'Calculs systèmes HVAC', '2024-02-08', '09:00:00', '11:00:00', 'Salle 205', 'Mme Petit', 'cours', 1);

-- FORMATEURS (pour enrichir les données)
INSERT IGNORE INTO formateurs (nom, prenom, email, telephone, specialites, experience_annees, secteur_principal, statut) VALUES
('Dubois', 'Jean', '<EMAIL>', '***********.89', 'Diagnostic automobile, Électronique embarquée', 15, 'automobile', 'actif'),
('Martin', 'Sophie', '<EMAIL>', '***********.90', 'Mécanique générale, Hydraulique', 12, 'mecanique', 'actif'),
('Leroy', 'Pierre', '<EMAIL>', '***********.91', 'Électrotechnique, Automatismes', 18, 'electricite', 'actif'),
('Rousseau', 'Marc', '<EMAIL>', '***********.92', 'Soudage, Chaudronnerie', 20, 'mecanique', 'actif'),
('Petit', 'Anne', '<EMAIL>', '***********.93', 'Génie thermique, Climatisation', 10, 'thermique', 'actif');

-- PARTENAIRES (pour page index.php)
INSERT IGNORE INTO partenaires (nom, description, secteur, type_partenariat, actif, ordre) VALUES
('Renault', 'Constructeur automobile français', 'Automobile', 'entreprise', TRUE, 1),
('Schneider Electric', 'Spécialiste de l\'énergie', 'Électricité', 'entreprise', TRUE, 2),
('Naval Group', 'Construction navale de défense', 'Naval', 'entreprise', TRUE, 3),
('Dassault Aviation', 'Constructeur aéronautique', 'Mécanique', 'entreprise', TRUE, 4),
('EDF', 'Producteur d\'électricité', 'Électricité', 'entreprise', TRUE, 5);

-- TÉMOIGNAGES (pour page index.php)
INSERT IGNORE INTO testimonials (nom, prenom, formation, entreprise, poste, temoignage, note, statut, date_publication) VALUES
('Dupont', 'Thomas', 'BTS Électrotechnique', 'Schneider Electric', 'Technicien Maintenance', 'Excellente formation qui m\'a permis de décrocher un emploi immédiatement après mon diplôme. Les formateurs sont très compétents.', 5, 'publie', '2024-01-01'),
('Moreau', 'Julie', 'CAP Maintenance Véhicules', 'Renault', 'Mécanicienne', 'Formation très pratique avec de vrais véhicules. J\'ai appris énormément et je recommande vivement cette école.', 5, 'publie', '2024-01-05'),
('Bernard', 'Antoine', 'Licence Pro Génie Thermique', 'Daikin', 'Ingénieur Études', 'Le niveau de formation est excellent. Les équipements sont modernes et les projets très formateurs.', 4, 'publie', '2024-01-10'),
('Roussel', 'Marie', 'Formation Continue Soudage', 'Naval Group', 'Soudeuse Qualifiée', 'Reconversion réussie grâce à cette formation de qualité. Encadrement professionnel et bienveillant.', 5, 'publie', '2024-01-15');

-- ÉQUIPEMENTS (pour pages secteurs et admin)
INSERT IGNORE INTO equipements (nom, description, secteur, salle, quantite, etat, date_acquisition, valeur) VALUES
('Robot Collaboratif UR10', 'Robot 6 axes pour formation robotique', 'mecanique', 'Atelier Robotique', 2, 'excellent', '2023-09-01', 45000.00),
('Banc Moteur Diesel', 'Banc d\'essai moteur avec acquisition données', 'automobile', 'Atelier Auto A', 1, 'bon', '2022-03-15', 25000.00),
('Automate Siemens S7-1500', 'Automate programmable industriel', 'electricite', 'Labo Élec 1', 8, 'excellent', '2023-01-10', 3500.00),
('Poste Soudage TIG', 'Poste soudage TIG/MIG professionnel', 'mecanique', 'Atelier Soudage', 6, 'bon', '2021-06-20', 4500.00),
('Pompe à Chaleur Didactique', 'Installation complète pour formation', 'thermique', 'Labo Thermique', 1, 'excellent', '2023-05-01', 15000.00);

-- PROJETS ÉTUDIANTS (pour enrichir le contenu)
INSERT IGNORE INTO projets_etudiants (titre, description, formation_id, secteur, etudiants, encadrant, date_debut, date_fin, statut, note) VALUES
('Véhicule Électrique Autonome', 'Développement d\'un prototype de véhicule électrique avec conduite autonome', 1, 'automobile', 'Thomas Dupont, Julie Martin, Pierre Leroy', 'M. Dubois', '2023-09-01', '2024-06-30', 'en_cours', NULL),
('Système Domotique IoT', 'Création d\'un système domotique connecté avec interface mobile', 2, 'electricite', 'Sophie Bernard, Antoine Roussel', 'M. Leroy', '2023-10-01', '2024-05-31', 'en_cours', NULL),
('Robot Soudeur Automatisé', 'Robot de soudage automatisé pour pièces complexes', 3, 'mecanique', 'Marie Petit, Jean Moreau', 'M. Rousseau', '2023-11-01', '2024-04-30', 'termine', 18.5),
('Pompe à Chaleur Intelligente', 'Système de chauffage intelligent avec IA', 4, 'thermique', 'Lucas Dubois, Emma Martin', 'Mme Petit', '2023-09-15', '2024-06-15', 'en_cours', NULL);

-- INSCRIPTIONS (pour admin/formations.php)
INSERT IGNORE INTO inscriptions (formation_id, nom, prenom, email, telephone, motivation, statut, date_inscription) VALUES
(1, 'Durand', 'Paul', '<EMAIL>', '***********.78', 'Passionné d\'automobile depuis toujours', 'accepte', '2024-01-15 10:30:00'),
(1, 'Lemoine', 'Sarah', '<EMAIL>', '***********.89', 'Reconversion professionnelle', 'en_attente', '2024-01-20 14:15:00'),
(2, 'Garnier', 'Maxime', '<EMAIL>', '***********.90', 'Intérêt pour les énergies renouvelables', 'accepte', '2024-01-18 09:45:00'),
(3, 'Roux', 'Camille', '<EMAIL>', '***********.01', 'Expérience en métallurgie', 'accepte', '2024-01-22 16:20:00'),
(4, 'Blanc', 'Nicolas', '<EMAIL>', '***********.12', 'Diplômé en génie mécanique', 'en_attente', '2024-01-25 11:10:00');

-- MESSAGES DE CONTACT (pour contact.php et admin)
INSERT IGNORE INTO contacts (nom, email, telephone, sujet, message, secteur, statut, date_creation) VALUES
('Martin', '<EMAIL>', '***********.89', 'Demande d\'information BTS', 'Bonjour, je souhaiterais avoir des informations sur le BTS Électrotechnique...', 'electricite', 'nouveau', '2024-01-20 10:30:00'),
('Dubois', '<EMAIL>', '***********.90', 'Inscription formation continue', 'Je suis intéressé par la formation continue en soudage...', 'mecanique', 'lu', '2024-01-18 14:15:00'),
('Leroy', '<EMAIL>', '***********.01', 'Partenariat entreprise', 'Notre entreprise souhaiterait établir un partenariat...', 'general', 'traite', '2024-01-15 09:20:00'),
('Rousseau', '<EMAIL>', '***********.12', 'Visite des installations', 'Possible de visiter vos ateliers avant inscription ?', 'automobile', 'nouveau', '2024-01-22 16:45:00');

-- DONNÉES SUPPLÉMENTAIRES POUR ADMIN
-- Mise à jour des statistiques pour le dashboard admin
UPDATE admin_users SET last_login = '2024-01-25 08:30:00' WHERE username = 'admin';
UPDATE admin_users SET last_login = '2024-01-24 14:20:00' WHERE username = 'editor';
