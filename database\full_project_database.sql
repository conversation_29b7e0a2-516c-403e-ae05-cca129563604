-- =====================================================
-- BASE DE DONNÉES COMPLÈTE POUR TOUTES LES PAGES
-- PROJET PÔLE INDUSTRIE - VERSION COMPLÈTE
-- =====================================================

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS pole_industrie CHARACTER SET utf8 COLLATE utf8_general_ci;
USE pole_industrie;

-- =====================================================
-- 1. TABLE DES UTILISATEURS ADMINISTRATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- =====================================================
-- 2. TABLE DES SECTEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS secteurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    couleur VARCHAR(7) DEFAULT '#1e3a8a',
    icone VARCHAR(50) DEFAULT 'fas fa-cog',
    ordre INT DEFAULT 0,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. TABLE DES FORMATIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duree VARCHAR(50),
    niveau ENUM('CAP', 'BTS', 'Licence Pro', 'Formation Continue', 'Formation Courte', 'Apprentissage') NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    prix DECIMAL(10,2),
    places_disponibles INT DEFAULT 0,
    date_debut DATE,
    date_fin DATE,
    lieu VARCHAR(255),
    statut ENUM('active', 'inactive', 'complete') DEFAULT 'active',
    image VARCHAR(255),
    brochure VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 4. TABLE DES ACTUALITÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    extrait TEXT,
    image VARCHAR(255),
    categorie ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') DEFAULT 'general',
    statut ENUM('brouillon', 'publie', 'archive') DEFAULT 'brouillon',
    featured BOOLEAN DEFAULT FALSE,
    date_publication DATE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 5. TABLE DES ÉVÉNEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME,
    lieu VARCHAR(255),
    type_evenement ENUM('conference', 'salon', 'portes_ouvertes', 'formation', 'autre') DEFAULT 'autre',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    places_max INT,
    places_reservees INT DEFAULT 0,
    prix DECIMAL(10,2) DEFAULT 0,
    image VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 6. TABLE DES EMPLOIS DU TEMPS
-- =====================================================
CREATE TABLE IF NOT EXISTS emplois_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    date_cours DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    salle VARCHAR(100),
    formateur VARCHAR(255),
    type_cours ENUM('cours', 'tp', 'td', 'examen', 'stage') DEFAULT 'cours',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 7. TABLE DES MESSAGES DE CONTACT
-- =====================================================
CREATE TABLE IF NOT EXISTS contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    sujet VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'thermique', 'automobile', 'navale', 'general') NULL,
    statut ENUM('nouveau', 'lu', 'traite', 'archive') DEFAULT 'nouveau',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    reponse TEXT NULL,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 8. TABLE DES INSCRIPTIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS inscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT NOT NULL,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    adresse TEXT,
    date_naissance DATE,
    niveau_etude VARCHAR(100),
    experience_professionnelle TEXT,
    motivation TEXT,
    statut ENUM('en_attente', 'accepte', 'refuse', 'liste_attente') DEFAULT 'en_attente',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    commentaires TEXT,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 9. TABLE DES PARAMÈTRES DU SITE
-- =====================================================
CREATE TABLE IF NOT EXISTS parametres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cle VARCHAR(100) UNIQUE NOT NULL,
    valeur TEXT,
    description TEXT,
    type ENUM('text', 'textarea', 'number', 'boolean', 'email', 'url') DEFAULT 'text',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 10. TABLE DES TÉMOIGNAGES
-- =====================================================
CREATE TABLE IF NOT EXISTS testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    formation VARCHAR(255),
    entreprise VARCHAR(255),
    poste VARCHAR(255),
    temoignage TEXT NOT NULL,
    note INT DEFAULT 5,
    photo VARCHAR(255),
    statut ENUM('en_attente', 'publie', 'archive') DEFAULT 'en_attente',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_publication DATE NULL
);

-- =====================================================
-- 11. TABLE DES PARTENAIRES
-- =====================================================
CREATE TABLE IF NOT EXISTS partenaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    logo VARCHAR(255),
    description TEXT,
    site_web VARCHAR(255),
    secteur VARCHAR(100),
    type_partenariat ENUM('entreprise', 'institutionnel', 'formation', 'equipement') DEFAULT 'entreprise',
    actif BOOLEAN DEFAULT TRUE,
    ordre INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 12. TABLE DES ÉQUIPEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS equipements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    salle VARCHAR(100),
    quantite INT DEFAULT 1,
    etat ENUM('excellent', 'bon', 'moyen', 'maintenance') DEFAULT 'bon',
    date_acquisition DATE,
    valeur DECIMAL(10,2),
    fournisseur VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 13. TABLE DES FORMATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS formateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    telephone VARCHAR(50),
    specialites TEXT,
    experience_annees INT,
    diplomes TEXT,
    photo VARCHAR(255),
    bio TEXT,
    secteur_principal ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general'),
    statut ENUM('actif', 'inactif', 'vacataire') DEFAULT 'actif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 14. TABLE DES PROJETS ÉTUDIANTS
-- =====================================================
CREATE TABLE IF NOT EXISTS projets_etudiants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    formation_id INT,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general'),
    etudiants TEXT,
    encadrant VARCHAR(255),
    date_debut DATE,
    date_fin DATE,
    statut ENUM('en_cours', 'termine', 'presente', 'prime') DEFAULT 'en_cours',
    image VARCHAR(255),
    fichiers TEXT,
    note DECIMAL(4,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE SET NULL
);

-- =====================================================
-- INSERTION DES DONNÉES PAR DÉFAUT
-- =====================================================

-- Utilisateurs admin
INSERT IGNORE INTO admin_users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$8K1p/wJIiU.5keowvUuYQOuiuiuqiUYqyMxdnQX9.vGA/l9dcCCO6', 'admin'),
('editor', '<EMAIL>', '$2y$10$8K1p/wJIiU.5keowvUuYQOuiuiuqiUYqyMxdnQX9.vGA/l9dcCCO6', 'editor');

-- Secteurs
INSERT IGNORE INTO secteurs (nom, slug, description, couleur, icone, ordre) VALUES
('Pôle Industrie de la Mécanique', 'mecanique', 'Formation aux métiers de la mécanique industrielle et de précision', '#1e3a8a', 'fas fa-cog', 1),
('Pôle Industrie de l\'Électricité', 'electricite', 'Expertise en systèmes électriques et électroniques industriels', '#dc2626', 'fas fa-bolt', 2),
('Pôle Génie Thermique', 'thermique', 'Spécialisation en systèmes de chauffage et climatisation', '#7c3aed', 'fas fa-thermometer-half', 3),
('Pôle Industrie de l\'Automobile', 'automobile', 'Formation aux technologies automobiles modernes', '#0ea5e9', 'fas fa-car', 4),
('Pôle Industrie Navale', 'navale', 'Métiers de la construction et réparation navale', '#0d9488', 'fas fa-ship', 5),
('Pôle Métiers de l\'Industrie', 'general', 'Formation transversale aux métiers industriels', '#16a34a', 'fas fa-tools', 6);

-- Paramètres du site
INSERT IGNORE INTO parametres (cle, valeur, description, type) VALUES
('site_name', 'Pôle Industrie', 'Nom du site', 'text'),
('site_description', 'Centre de formation et d\'excellence industrielle', 'Description du site', 'textarea'),
('contact_email', '<EMAIL>', 'Email de contact principal', 'email'),
('contact_phone', '+33 1 23 45 67 89', 'Téléphone de contact', 'text'),
('contact_address', '123 Avenue de l\'Industrie, 75000 Paris, France', 'Adresse de contact', 'textarea'),
('facebook_url', 'https://facebook.com/poleindustrie', 'URL Facebook', 'url'),
('linkedin_url', 'https://linkedin.com/company/poleindustrie', 'URL LinkedIn', 'url'),
('twitter_url', 'https://twitter.com/poleindustrie', 'URL Twitter', 'url'),
('youtube_url', 'https://youtube.com/poleindustrie', 'URL YouTube', 'url');
