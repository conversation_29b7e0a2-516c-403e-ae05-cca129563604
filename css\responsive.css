/* Responsive Design */

/* Tablettes */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .secteurs-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .main-nav ul {
        gap: 1rem;
    }
    
    .main-nav a {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

/* Mobiles */
@media (max-width: 768px) {
    .header-content {
        justify-content: space-between;
    }
    
    .main-nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .mobile-nav {
        display: block;
    }
    
    .hero {
        padding: 3rem 0;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .secteurs-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 2rem 0;
    }
    
    .secteur-card {
        padding: 1.5rem;
    }
    
    .section {
        padding: 2rem 0;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .logo p {
        font-size: 0.8rem;
    }
}

/* Petits mobiles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .hero h1 {
        font-size: 1.8rem;
    }
    
    .secteur-card {
        padding: 1rem;
    }
    
    .secteur-icon {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Animation pour le menu mobile */
.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Animations d'apparition */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

.fade-in-delay-1 {
    animation-delay: 0.2s;
}

.fade-in-delay-2 {
    animation-delay: 0.4s;
}

.fade-in-delay-3 {
    animation-delay: 0.6s;
}

/* Hover effects pour les cartes */
@media (hover: hover) {
    .secteur-card:hover .secteur-icon {
        transform: scale(1.1);
        transition: transform 0.3s ease;
    }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --light-gray: #1f2937;
        --dark-gray: #f8fafc;
    }
    
    body {
        background-color: #111827;
        color: #f8fafc;
    }
    
    .secteur-card {
        background: #374151;
        color: #f8fafc;
    }
}

/* Print styles */
@media print {
    .main-header,
    .main-footer,
    .mobile-nav,
    .cta-buttons {
        display: none;
    }
    
    .hero {
        background: none;
        color: #000;
    }
    
    .secteur-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
