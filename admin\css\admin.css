/* Styles pour l'interface d'administration */

/* Variables spécifiques à l'admin */
:root {
    --admin-primary: #1e3a8a;
    --admin-secondary: #64748b;
    --admin-success: #16a34a;
    --admin-warning: #f59e0b;
    --admin-danger: #dc2626;
    --admin-info: #0ea5e9;
    --admin-light: #f8fafc;
    --admin-dark: #1e293b;
    --admin-border: #e2e8f0;
    --admin-sidebar: #334155;
}

/* Page de connexion */
.admin-login {
    background: linear-gradient(135deg, #1e3a8a, #0d9488);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.admin-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.login-container {
    width: 100%;
    max-width: 400px;
    position: relative;
    z-index: 2;
}

.login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 50px rgba(0,0,0,0.15);
    overflow: hidden;
    animation: fadeInUp 1s ease;
}

.login-header {
    background: linear-gradient(135deg, #1e3a8a, #0d9488);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.login-header .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.login-header .logo i {
    font-size: 2rem;
}

.login-header h1 {
    font-size: 1.5rem;
    margin: 0;
}

.login-header h2 {
    font-size: 1.2rem;
    margin: 1rem 0 0.5rem;
    opacity: 0.9;
}

.login-header p {
    opacity: 0.8;
    margin: 0;
}

.login-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--admin-dark);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--admin-border);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.btn-full {
    width: 100%;
    justify-content: center;
    background: linear-gradient(135deg, #1e3a8a, #0d9488);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-full:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.login-footer {
    padding: 1rem 2rem 2rem;
    text-align: center;
}

.login-footer a {
    color: var(--admin-secondary);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.login-footer a:hover {
    color: #1e3a8a;
}

/* Animation pour la page de connexion */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Alertes */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
    border-left: 4px solid #dc2626;
}

.alert-success {
    background: #f0fdf4;
    color: var(--admin-success);
    border: 1px solid #bbf7d0;
}

.alert-warning {
    background: #fffbeb;
    color: var(--admin-warning);
    border: 1px solid #fed7aa;
}

.alert-info {
    background: #eff6ff;
    color: var(--admin-info);
    border: 1px solid #bfdbfe;
}

/* Layout principal de l'admin */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background: var(--admin-light);
}

.admin-sidebar {
    width: 280px;
    background: var(--admin-sidebar);
    color: white;
    flex-shrink: 0;
}

.admin-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.admin-header {
    background: white;
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* Sidebar */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    padding: 0.5rem 1.5rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    opacity: 0.7;
    font-weight: 600;
}

.nav-item {
    display: block;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover,
.nav-item.active {
    background: rgba(255,255,255,0.1);
    color: white;
    border-left-color: var(--admin-info);
}

.nav-item i {
    width: 20px;
    margin-right: 0.75rem;
}

/* Header admin */
.admin-title h1 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--admin-dark);
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: var(--admin-dark);
}

.user-role {
    font-size: 0.875rem;
    color: var(--admin-secondary);
}

.btn-logout {
    background: var(--admin-danger);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: background 0.3s ease;
}

.btn-logout:hover {
    background: #b91c1c;
}

/* Dashboard */
.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    margin: 0 0 0.5rem;
    color: var(--admin-dark);
}

.dashboard-header p {
    color: var(--admin-secondary);
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    border: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.actualites { background: var(--admin-info); }
.stat-icon.formations { background: var(--admin-success); }
.stat-icon.evenements { background: var(--admin-warning); }
.stat-icon.users { background: var(--admin-primary); }

.stat-content h3 {
    margin: 0 0 0.25rem;
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-dark);
}

.stat-content p {
    margin: 0 0 0.25rem;
    font-weight: 600;
    color: var(--admin-dark);
}

.stat-content small {
    color: var(--admin-secondary);
}

/* Actions rapides */
.quick-actions {
    margin-bottom: 3rem;
}

.quick-actions h2 {
    margin-bottom: 1rem;
    color: var(--admin-dark);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: white;
    border: 2px solid var(--admin-border);
    border-radius: 12px;
    padding: 1.5rem;
    text-decoration: none;
    color: var(--admin-dark);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
}

.action-card:hover {
    border-color: var(--admin-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.action-card i {
    font-size: 2rem;
    color: var(--admin-primary);
}

.action-card span {
    font-weight: 500;
    text-align: center;
}

/* Activité récente */
.recent-activity h2 {
    margin-bottom: 1rem;
    color: var(--admin-dark);
}

.activity-list {
    background: white;
    border-radius: 12px;
    border: 1px solid var(--admin-border);
    overflow: hidden;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--admin-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--admin-secondary);
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin: 0 0 0.25rem;
    font-size: 0.95rem;
    color: var(--admin-dark);
}

.activity-content p {
    margin: 0 0 0.25rem;
    font-size: 0.875rem;
    color: var(--admin-secondary);
}

.activity-content small {
    color: var(--admin-secondary);
    font-size: 0.8rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.publie {
    background: #dcfce7;
    color: var(--admin-success);
}

.status-badge.brouillon {
    background: #fef3c7;
    color: var(--admin-warning);
}

.status-badge.archive {
    background: #f1f5f9;
    color: var(--admin-secondary);
}

.no-activity {
    padding: 2rem;
    text-align: center;
    color: var(--admin-secondary);
}

.no-activity i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-layout {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        order: 2;
    }

    .admin-main {
        order: 1;
    }

    .admin-header {
        padding: 1rem;
    }

    .admin-content {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Pages de gestion */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.page-title h1 {
    margin: 0 0 0.5rem;
    color: var(--admin-dark);
}

.page-title p {
    margin: 0;
    color: var(--admin-secondary);
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* Filtres */
.filters-bar {
    background: white;
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.filters-form {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid var(--admin-border);
    border-radius: 4px;
    min-width: 150px;
}

/* Actions en lot */
.bulk-actions {
    background: var(--admin-light);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;
}

.bulk-actions select {
    padding: 0.5rem;
    border: 1px solid var(--admin-border);
    border-radius: 4px;
}

/* Tableaux de données */
.data-table {
    background: white;
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    overflow: hidden;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--admin-border);
}

.data-table th {
    background: var(--admin-light);
    font-weight: 600;
    color: var(--admin-dark);
}

.data-table tr:hover {
    background: #f8fafc;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.item-title strong {
    display: block;
    margin-bottom: 0.25rem;
}

.item-meta {
    font-size: 0.875rem;
    color: var(--admin-secondary);
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.badge.featured {
    background: #fef3c7;
    color: var(--admin-warning);
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
}

.category-badge.mecanique { background: #dbeafe; color: var(--admin-primary); }
.category-badge.electricite { background: #fecaca; color: var(--admin-danger); }
.category-badge.automobile { background: #bfdbfe; color: var(--admin-info); }
.category-badge.navale { background: #a7f3d0; color: var(--admin-success); }
.category-badge.thermique { background: #e9d5ff; color: #7c3aed; }
.category-badge.general { background: #f1f5f9; color: var(--admin-secondary); }

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid var(--admin-border);
    border-radius: 4px;
    color: var(--admin-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

.btn-icon.btn-danger:hover {
    background: var(--admin-danger);
    border-color: var(--admin-danger);
}

.no-data {
    text-align: center;
    padding: 3rem;
    color: var(--admin-secondary);
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-data p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Formulaires */
.form-container {
    background: white;
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    overflow: hidden;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 300px;
    min-height: 600px;
}

.form-main {
    padding: 2rem;
    border-right: 1px solid var(--admin-border);
}

.form-sidebar {
    background: var(--admin-light);
    padding: 2rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section h3 {
    margin: 0 0 1rem;
    color: var(--admin-dark);
    font-size: 1.1rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--admin-border);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--admin-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--admin-primary);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group textarea.rich-editor {
    min-height: 300px;
    font-family: 'Courier New', monospace;
}

/* Checkbox personnalisé */
.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    width: auto !important;
    margin: 0;
}

.checkmark {
    font-weight: normal;
}

/* Actions du formulaire */
.form-actions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--admin-border);
}

.form-actions .btn {
    margin-bottom: 0.75rem;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Éditeur de texte */
.editor-toolbar {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: var(--admin-light);
    border: 1px solid var(--admin-border);
    border-bottom: none;
    border-radius: 4px 4px 0 0;
}

.editor-toolbar button {
    padding: 0.5rem;
    border: 1px solid var(--admin-border);
    background: white;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.editor-toolbar button:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid var(--admin-success);
}

.notification-error {
    border-left: 4px solid var(--admin-danger);
}

.notification-info {
    border-left: 4px solid var(--admin-info);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--admin-secondary);
    margin-left: auto;
}

.notification-close:hover {
    color: var(--admin-dark);
}

/* Responsive pour formulaires */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-main {
        border-right: none;
        border-bottom: 1px solid var(--admin-border);
    }

    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .filter-group input,
    .filter-group select {
        min-width: auto;
    }
}

/* Badges et étiquettes supplémentaires */
.level-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.level-badge.cap { background: #fef3c7; color: #92400e; }
.level-badge.bts { background: #dbeafe; color: #1e40af; }
.level-badge.licence-pro { background: #e9d5ff; color: #7c3aed; }
.level-badge.formation-continue { background: #d1fae5; color: #065f46; }
.level-badge.formation-courte { background: #fed7d7; color: #c53030; }
.level-badge.apprentissage { background: #e0e7ff; color: #3730a3; }

.type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.type-badge.cours { background: #dbeafe; color: #1e40af; }
.type-badge.tp { background: #dcfce7; color: #166534; }
.type-badge.td { background: #fef3c7; color: #92400e; }
.type-badge.examen { background: #fecaca; color: #991b1b; }
.type-badge.stage { background: #e9d5ff; color: #7c3aed; }
.type-badge.conference { background: #e0e7ff; color: #3730a3; }
.type-badge.salon { background: #fed7d7; color: #c53030; }
.type-badge.portes_ouvertes { background: #d1fae5; color: #065f46; }
.type-badge.formation { background: #dbeafe; color: #1e40af; }
.type-badge.autre { background: #f1f5f9; color: #64748b; }

.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.role-badge.admin { background: #fecaca; color: #991b1b; }
.role-badge.editor { background: #dbeafe; color: #1e40af; }

.badge.current-user {
    background: #dcfce7;
    color: #166534;
    margin-left: 0.5rem;
}

.places-count {
    font-weight: 600;
    color: var(--admin-primary);
}

.formation-link {
    color: var(--admin-primary);
    text-decoration: none;
    font-weight: 500;
}

.formation-link:hover {
    text-decoration: underline;
}

.date-info {
    text-align: center;
}

.date-info strong {
    display: block;
    margin-bottom: 0.25rem;
}

/* Formulaires - rangées */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--admin-secondary);
    line-height: 1.4;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--admin-border);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 600;
    color: var(--admin-dark);
    margin: 0;
}

.info-item span {
    color: var(--admin-secondary);
}

/* Messages d'erreur dans les formulaires */
.form-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #991b1b;
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

/* Amélioration des tableaux */
.data-table tr.data-row:hover {
    background: #f8fafc;
}

.text-muted {
    color: var(--admin-secondary);
    font-style: italic;
}

/* Responsive pour les nouvelles rangées */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .date-info {
        text-align: left;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Gestion des secteurs */
.order-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: var(--admin-light);
    border: 1px solid var(--admin-border);
    border-radius: 50%;
    font-weight: 600;
    color: var(--admin-dark);
}

.color-preview {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.color-swatch {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: 2px solid white;
    box-shadow: 0 0 0 1px var(--admin-border);
}

code {
    background: var(--admin-light);
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: var(--admin-dark);
}

/* Statuts actif/inactif */
.status-badge.active {
    background: #dcfce7;
    color: var(--admin-success);
}

.status-badge.inactive {
    background: #f1f5f9;
    color: var(--admin-secondary);
}

/* Amélioration des champs de couleur */
input[type="color"] {
    width: 60px !important;
    height: 40px !important;
    padding: 0 !important;
    border: 2px solid var(--admin-border) !important;
    border-radius: 4px !important;
    cursor: pointer;
}

input[type="color"]:focus {
    border-color: var(--admin-primary) !important;
}

/* Messages de confirmation */
.confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.confirm-content {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    max-width: 400px;
    text-align: center;
}

.confirm-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

/* Amélioration de l'accessibilité */
.btn:focus,
.btn-icon:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid var(--admin-primary);
    outline-offset: 2px;
}

/* Animation pour les suppressions */
.data-row.deleting {
    opacity: 0.5;
    transform: scale(0.98);
    transition: all 0.3s ease;
}

/* Indicateur de chargement */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--admin-border);
    border-top-color: var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Amélioration des tooltips */
[title] {
    position: relative;
}

/* États de validation des formulaires */
.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
    border-color: var(--admin-danger);
}

.form-group.has-success input,
.form-group.has-success select,
.form-group.has-success textarea {
    border-color: var(--admin-success);
}

.field-error {
    color: var(--admin-danger);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.field-success {
    color: var(--admin-success);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Page de paramètres */
.settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    background: white;
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    overflow: hidden;
}

.settings-nav {
    background: var(--admin-light);
    border-right: 1px solid var(--admin-border);
    padding: 1rem 0;
}

.settings-nav .nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--admin-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.settings-nav .nav-item:hover,
.settings-nav .nav-item.active {
    background: white;
    color: var(--admin-primary);
    border-left-color: var(--admin-primary);
}

.settings-content {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    margin: 0 0 1.5rem;
    color: var(--admin-dark);
    font-size: 1.2rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--admin-border);
}

.settings-section h4 {
    margin: 1.5rem 0 1rem;
    color: var(--admin-dark);
    font-size: 1rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--admin-border);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 600;
    color: var(--admin-dark);
    margin: 0;
}

.setting-value {
    text-align: right;
}

.setting-value strong {
    display: block;
    margin-bottom: 0.25rem;
}

.setting-value small {
    color: var(--admin-secondary);
    font-size: 0.875rem;
}

.text-error {
    color: var(--admin-danger);
}

/* Outils de maintenance */
.maintenance-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.tool-card {
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    transition: all 0.3s ease;
}

.tool-card:hover {
    border-color: var(--admin-primary);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.tool-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: var(--admin-light);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--admin-primary);
}

.tool-content h4 {
    margin: 0 0 0.5rem;
    color: var(--admin-dark);
}

.tool-content p {
    margin: 0 0 1rem;
    color: var(--admin-secondary);
    font-size: 0.875rem;
}

/* Section À propos */
.about-info {
    text-align: center;
}

.about-logo {
    margin-bottom: 2rem;
}

.about-logo i {
    font-size: 4rem;
    color: var(--admin-primary);
    margin-bottom: 1rem;
}

.about-logo h2 {
    margin: 0;
    color: var(--admin-dark);
}

.about-details {
    max-width: 600px;
    margin: 0 auto;
    text-align: left;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid var(--admin-border);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 600;
    color: var(--admin-dark);
    margin: 0;
    min-width: 150px;
}

.detail-item span {
    color: var(--admin-secondary);
}

.detail-item ul {
    margin: 0;
    padding-left: 1rem;
    color: var(--admin-secondary);
}

.detail-item li {
    margin-bottom: 0.25rem;
}

/* Responsive pour les paramètres */
@media (max-width: 768px) {
    .settings-container {
        grid-template-columns: 1fr;
    }

    .settings-nav {
        border-right: none;
        border-bottom: 1px solid var(--admin-border);
        display: flex;
        overflow-x: auto;
        padding: 0;
    }

    .settings-nav .nav-item {
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .settings-nav .nav-item.active {
        border-left: none;
        border-bottom-color: var(--admin-primary);
    }

    .maintenance-tools {
        grid-template-columns: 1fr;
    }

    .tool-card {
        flex-direction: column;
        text-align: center;
    }

    .setting-item,
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .setting-value {
        text-align: left;
    }
}
