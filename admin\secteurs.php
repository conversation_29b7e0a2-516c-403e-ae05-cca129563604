<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification et les droits admin
requireRole('admin');

$page_title = "Gestion des secteurs";
$current_user = getCurrentUser();

// Gestion des actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

try {
    $pdo = getDBConnection();
    
    // Créer la table secteurs si elle n'existe pas
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS secteurs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(255) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            couleur VARCHAR(7) DEFAULT '#1e3a8a',
            icone VARCHAR(50) DEFAULT 'fas fa-cog',
            ordre INT DEFAULT 0,
            actif BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Insérer les secteurs par défaut s'ils n'existent pas
    $stmt = $pdo->query("SELECT COUNT(*) FROM secteurs");
    if ($stmt->fetchColumn() == 0) {
        $secteurs_defaut = [
            ['Pôle Industrie de la Mécanique', 'mecanique', 'Formation aux métiers de la mécanique industrielle et de précision', '#1e3a8a', 'fas fa-cog', 1],
            ['Pôle Industrie de l\'Électricité', 'electricite', 'Expertise en systèmes électriques et électroniques industriels', '#dc2626', 'fas fa-bolt', 2],
            ['Pôle Génie Thermique', 'thermique', 'Spécialisation en systèmes de chauffage et climatisation', '#7c3aed', 'fas fa-thermometer-half', 3],
            ['Pôle Industrie de l\'Automobile', 'automobile', 'Formation aux technologies automobiles modernes', '#0ea5e9', 'fas fa-car', 4],
            ['Pôle Industrie Navale', 'navale', 'Métiers de la construction et réparation navale', '#0d9488', 'fas fa-ship', 5],
            ['Pôle Métiers de l\'Industrie', 'general', 'Formation transversale aux métiers industriels', '#16a34a', 'fas fa-tools', 6]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO secteurs (nom, slug, description, couleur, icone, ordre) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($secteurs_defaut as $secteur) {
            $stmt->execute($secteur);
        }
    }
    
    // Traitement des actions POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Sauvegarde de secteur
        if (isset($_POST['save_secteur'])) {
            $nom = trim($_POST['nom'] ?? '');
            $slug = trim($_POST['slug'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $couleur = $_POST['couleur'] ?? '#1e3a8a';
            $icone = trim($_POST['icone'] ?? 'fas fa-cog');
            $ordre = $_POST['ordre'] ?? 0;
            $actif = isset($_POST['actif']) ? 1 : 0;
            
            if (empty($nom) || empty($slug)) {
                $error = "Le nom et le slug sont obligatoires";
            } else {
                try {
                    if ($action === 'add') {
                        $stmt = $pdo->prepare("
                            INSERT INTO secteurs (nom, slug, description, couleur, icone, ordre, actif) 
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$nom, $slug, $description, $couleur, $icone, $ordre, $actif]);
                        $message = "Secteur créé avec succès";
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE secteurs 
                            SET nom = ?, slug = ?, description = ?, couleur = ?, icone = ?, ordre = ?, actif = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$nom, $slug, $description, $couleur, $icone, $ordre, $actif, $id]);
                        $message = "Secteur modifié avec succès";
                    }
                    
                    header("Location: secteurs.php?message=" . urlencode($message));
                    exit;
                    
                } catch (PDOException $e) {
                    if ($e->getCode() == 23000) {
                        $error = "Ce slug existe déjà";
                    } else {
                        $error = "Erreur lors de la sauvegarde : " . $e->getMessage();
                    }
                }
            }
        }
    }
    
    // Traitement des actions GET
    if ($action === 'delete' && $id) {
        $stmt = $pdo->prepare("DELETE FROM secteurs WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Secteur supprimé avec succès";
        $action = 'list';
    } elseif ($action === 'toggle_status' && $id) {
        $stmt = $pdo->prepare("UPDATE secteurs SET actif = NOT actif WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Statut modifié avec succès";
        $action = 'list';
    }
    
    // Récupération des secteurs pour la liste
    if ($action === 'list') {
        $stmt = $pdo->query("SELECT * FROM secteurs ORDER BY ordre ASC, nom ASC");
        $secteurs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = "Erreur de base de données : " . $e->getMessage();
}

include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success" data-dismiss="auto">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <div class="page-header">
        <div class="page-title">
            <h1>Gestion des secteurs</h1>
            <p>Configurez les secteurs industriels et leurs couleurs</p>
        </div>
        <div class="page-actions">
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Ajouter un secteur
            </a>
        </div>
    </div>
    
    <!-- Tableau des secteurs -->
    <div class="data-table">
        <table>
            <thead>
                <tr>
                    <th>Ordre</th>
                    <th>Secteur</th>
                    <th>Slug</th>
                    <th>Couleur</th>
                    <th>Icône</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($secteurs)): ?>
                    <tr>
                        <td colspan="7" class="no-data">
                            <i class="fas fa-cogs"></i>
                            <p>Aucun secteur trouvé</p>
                            <a href="?action=add" class="btn btn-primary">Créer le premier secteur</a>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($secteurs as $secteur): ?>
                        <tr class="data-row">
                            <td>
                                <span class="order-badge"><?php echo $secteur['ordre']; ?></span>
                            </td>
                            <td>
                                <div class="item-title">
                                    <strong><?php echo htmlspecialchars($secteur['nom']); ?></strong>
                                    <div class="item-meta"><?php echo htmlspecialchars($secteur['description']); ?></div>
                                </div>
                            </td>
                            <td>
                                <code><?php echo htmlspecialchars($secteur['slug']); ?></code>
                            </td>
                            <td>
                                <div class="color-preview">
                                    <span class="color-swatch" style="background-color: <?php echo $secteur['couleur']; ?>"></span>
                                    <code><?php echo $secteur['couleur']; ?></code>
                                </div>
                            </td>
                            <td>
                                <i class="<?php echo $secteur['icone']; ?>" style="color: <?php echo $secteur['couleur']; ?>"></i>
                                <code><?php echo $secteur['icone']; ?></code>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $secteur['actif'] ? 'active' : 'inactive'; ?>">
                                    <?php echo $secteur['actif'] ? 'Actif' : 'Inactif'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="?action=edit&id=<?php echo $secteur['id']; ?>" class="btn-icon" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="?action=toggle_status&id=<?php echo $secteur['id']; ?>" class="btn-icon" title="Changer le statut">
                                        <i class="fas fa-toggle-<?php echo $secteur['actif'] ? 'on' : 'off'; ?>"></i>
                                    </a>
                                    <a href="?action=delete&id=<?php echo $secteur['id']; ?>" class="btn-icon btn-danger" 
                                       data-action="delete" data-item="<?php echo htmlspecialchars($secteur['nom']); ?>" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <?php
    $secteur = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM secteurs WHERE id = ?");
        $stmt->execute([$id]);
        $secteur = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$secteur) {
            $error = "Secteur non trouvé";
            $action = 'list';
        }
    }
    ?>
    
    <div class="page-header">
        <div class="page-title">
            <h1><?php echo $action === 'add' ? 'Ajouter' : 'Modifier'; ?> un secteur</h1>
            <p>Configurez un secteur industriel avec ses couleurs et icônes</p>
        </div>
        <div class="page-actions">
            <a href="secteurs.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Retour à la liste
            </a>
        </div>
    </div>
    
    <form method="POST" class="form-container">
        <div class="form-grid">
            <div class="form-main">
                <div class="form-section">
                    <h3>Informations générales</h3>
                    
                    <div class="form-group">
                        <label for="nom">Nom du secteur *</label>
                        <input type="text" id="nom" name="nom" required 
                               value="<?php echo htmlspecialchars($secteur['nom'] ?? ''); ?>"
                               placeholder="Ex: Pôle Industrie de la Mécanique">
                    </div>
                    
                    <div class="form-group">
                        <label for="slug">Slug *</label>
                        <input type="text" id="slug" name="slug" required 
                               value="<?php echo htmlspecialchars($secteur['slug'] ?? ''); ?>"
                               placeholder="Ex: mecanique (sans espaces, caractères spéciaux)">
                        <small class="form-help">Identifiant unique utilisé dans les URLs et le code</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4"
                                  placeholder="Description du secteur et de ses spécialités"><?php echo htmlspecialchars($secteur['description'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>
            
            <div class="form-sidebar">
                <div class="form-section">
                    <h3>Apparence</h3>
                    
                    <div class="form-group">
                        <label for="couleur">Couleur principale</label>
                        <input type="color" id="couleur" name="couleur" 
                               value="<?php echo $secteur['couleur'] ?? '#1e3a8a'; ?>">
                        <small class="form-help">Couleur utilisée pour identifier ce secteur</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="icone">Icône FontAwesome</label>
                        <input type="text" id="icone" name="icone" 
                               value="<?php echo htmlspecialchars($secteur['icone'] ?? 'fas fa-cog'); ?>"
                               placeholder="fas fa-cog">
                        <small class="form-help">Classe CSS FontAwesome (ex: fas fa-cog)</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="ordre">Ordre d'affichage</label>
                        <input type="number" id="ordre" name="ordre" min="0"
                               value="<?php echo $secteur['ordre'] ?? '0'; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="actif" value="1" 
                                   <?php echo ($secteur['actif'] ?? true) ? 'checked' : ''; ?>>
                            <span class="checkmark"></span>
                            Secteur actif
                        </label>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" name="save_secteur" class="btn btn-primary btn-full">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer' : 'Mettre à jour'; ?>
                    </button>
                    
                    <?php if ($action === 'edit'): ?>
                        <a href="secteurs.php?action=delete&id=<?php echo $id; ?>" 
                           class="btn btn-danger btn-full" 
                           data-action="delete" data-item="<?php echo htmlspecialchars($secteur['nom']); ?>">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </form>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
