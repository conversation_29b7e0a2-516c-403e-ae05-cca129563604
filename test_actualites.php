<?php
// Test simple pour vérifier les actualités
require_once 'includes/config.php';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Test Actualités</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .actualite { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test des Actualités</h1>";

try {
    echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
    
    $pdo = getDBConnection();
    
    // Test de la requête exacte utilisée dans actualites.php
    echo "<h2>Requête utilisée dans actualites.php :</h2>";
    echo "<code>SELECT * FROM actualites WHERE statut = 'publie' ORDER BY date_publication DESC</code>";
    
    $stmt = $pdo->query("SELECT * FROM actualites WHERE statut = 'publie' ORDER BY date_publication DESC");
    $actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='success'>✅ Requête exécutée avec succès</div>";
    echo "<p><strong>Nombre d'actualités trouvées :</strong> " . count($actualites) . "</p>";
    
    // Test requête featured
    echo "<h2>Requête actualités à la une :</h2>";
    echo "<code>SELECT * FROM actualites WHERE statut = 'publie' AND featured = 1 ORDER BY date_publication DESC LIMIT 3</code>";
    
    $stmt = $pdo->query("SELECT * FROM actualites WHERE statut = 'publie' AND featured = 1 ORDER BY date_publication DESC LIMIT 3");
    $actualites_featured = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Nombre d'actualités à la une :</strong> " . count($actualites_featured) . "</p>";
    
    if (empty($actualites)) {
        echo "<div class='error'>❌ Aucune actualité trouvée !</div>";
        
        // Vérifier s'il y a des actualités dans la table
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM actualites");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<p>Total actualités dans la table : $total</p>";
        
        if ($total > 0) {
            // Vérifier les statuts
            $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM actualites GROUP BY statut");
            $statuts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<h3>Répartition par statut :</h3>";
            foreach ($statuts as $statut) {
                echo "<p>- " . $statut['statut'] . " : " . $statut['count'] . "</p>";
            }
            
            // Afficher quelques actualités pour debug
            $stmt = $pdo->query("SELECT id, titre, statut, featured FROM actualites LIMIT 5");
            $debug_actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<h3>Premières actualités (debug) :</h3>";
            foreach ($debug_actualites as $a) {
                echo "<p>ID: {$a['id']}, Titre: {$a['titre']}, Statut: {$a['statut']}, Featured: {$a['featured']}</p>";
            }
        } else {
            echo "<div class='error'>❌ La table actualites est vide ! Vous devez installer les données.</div>";
            echo "<p><a href='database/install_database.php'>Installer la base de données</a></p>";
        }
    } else {
        echo "<h2>Actualités trouvées :</h2>";
        
        foreach ($actualites as $actualite) {
            echo "<div class='actualite'>";
            echo "<h3>" . htmlspecialchars($actualite['titre']) . "</h3>";
            echo "<p><strong>Catégorie :</strong> " . htmlspecialchars($actualite['categorie']) . "</p>";
            echo "<p><strong>Statut :</strong> " . htmlspecialchars($actualite['statut']) . "</p>";
            echo "<p><strong>À la une :</strong> " . ($actualite['featured'] ? 'Oui' : 'Non') . "</p>";
            echo "<p><strong>Date :</strong> " . htmlspecialchars($actualite['date_publication']) . "</p>";
            echo "<p><strong>Extrait :</strong> " . htmlspecialchars($actualite['extrait']) . "</p>";
            echo "</div>";
        }
        
        echo "<div class='success'>✅ Les actualités sont correctement récupérées !</div>";
        echo "<p>Le problème ne vient pas de la base de données.</p>";
        echo "<p><a href='actualites.php'>Tester la page actualites.php</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur : " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<p>Vérifiez que :</p>";
    echo "<ul>";
    echo "<li>MySQL est démarré</li>";
    echo "<li>La base de données 'pole_industrie' existe</li>";
    echo "<li>Les tables sont créées</li>";
    echo "</ul>";
    echo "<p><a href='database/install_database.php'>Installer la base de données</a></p>";
}

echo "</body></html>";
?>
