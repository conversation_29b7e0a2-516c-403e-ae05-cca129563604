<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification et les droits admin
requireRole('admin');

$page_title = "Paramètres généraux";
$current_user = getCurrentUser();

$message = '';
$error = '';

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_settings'])) {
        // Ici vous pourriez sauvegarder les paramètres dans une table de configuration
        $message = "Paramètres sauvegardés avec succès";
    }
}

include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success" data-dismiss="auto">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="page-header">
    <div class="page-title">
        <h1>Paramètres généraux</h1>
        <p>Configuration globale de l'interface d'administration</p>
    </div>
</div>

<div class="settings-container">
    <div class="settings-nav">
        <div class="nav-item active" data-tab="general">
            <i class="fas fa-cog"></i>
            Général
        </div>
        <div class="nav-item" data-tab="database">
            <i class="fas fa-database"></i>
            Base de données
        </div>
        <div class="nav-item" data-tab="maintenance">
            <i class="fas fa-tools"></i>
            Maintenance
        </div>
        <div class="nav-item" data-tab="about">
            <i class="fas fa-info-circle"></i>
            À propos
        </div>
    </div>
    
    <div class="settings-content">
        <!-- Onglet Général -->
        <div id="general" class="tab-content active">
            <div class="settings-section">
                <h3>Configuration du site</h3>
                
                <div class="setting-item">
                    <label>Nom du site</label>
                    <div class="setting-value">
                        <strong><?php echo SITE_NAME; ?></strong>
                        <small>Défini dans includes/config.php</small>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>Description</label>
                    <div class="setting-value">
                        <strong><?php echo SITE_DESCRIPTION; ?></strong>
                        <small>Défini dans includes/config.php</small>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>Version PHP</label>
                    <div class="setting-value">
                        <strong><?php echo PHP_VERSION; ?></strong>
                        <span class="status-badge <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'active' : 'inactive'; ?>">
                            <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'Compatible' : 'Obsolète'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Onglet Base de données -->
        <div id="database" class="tab-content">
            <div class="settings-section">
                <h3>Informations de la base de données</h3>
                
                <?php
                try {
                    $pdo = getDBConnection();
                    
                    // Informations de connexion
                    echo '<div class="setting-item">';
                    echo '<label>Serveur</label>';
                    echo '<div class="setting-value"><strong>' . DB_HOST . '</strong></div>';
                    echo '</div>';
                    
                    echo '<div class="setting-item">';
                    echo '<label>Base de données</label>';
                    echo '<div class="setting-value"><strong>' . DB_NAME . '</strong></div>';
                    echo '</div>';
                    
                    echo '<div class="setting-item">';
                    echo '<label>Utilisateur</label>';
                    echo '<div class="setting-value"><strong>' . DB_USER . '</strong></div>';
                    echo '</div>';
                    
                    // Statistiques des tables
                    $tables = ['admin_users', 'actualites', 'formations', 'evenements', 'emplois_temps'];
                    echo '<h4>Statistiques des tables</h4>';
                    
                    foreach ($tables as $table) {
                        try {
                            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                            $count = $stmt->fetchColumn();
                            
                            echo '<div class="setting-item">';
                            echo '<label>' . ucfirst(str_replace('_', ' ', $table)) . '</label>';
                            echo '<div class="setting-value"><strong>' . $count . '</strong> enregistrements</div>';
                            echo '</div>';
                        } catch (Exception $e) {
                            echo '<div class="setting-item">';
                            echo '<label>' . ucfirst(str_replace('_', ' ', $table)) . '</label>';
                            echo '<div class="setting-value"><span class="text-error">Table non trouvée</span></div>';
                            echo '</div>';
                        }
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="alert alert-error">';
                    echo '<i class="fas fa-exclamation-triangle"></i>';
                    echo 'Erreur de connexion à la base de données : ' . $e->getMessage();
                    echo '</div>';
                }
                ?>
            </div>
        </div>
        
        <!-- Onglet Maintenance -->
        <div id="maintenance" class="tab-content">
            <div class="settings-section">
                <h3>Outils de maintenance</h3>
                
                <div class="maintenance-tools">
                    <div class="tool-card">
                        <div class="tool-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="tool-content">
                            <h4>Réinstaller la base de données</h4>
                            <p>Recrée toutes les tables et réinitialise les données par défaut</p>
                            <a href="install.php" class="btn btn-warning">
                                <i class="fas fa-redo"></i>
                                Réinstaller
                            </a>
                        </div>
                    </div>
                    
                    <div class="tool-card">
                        <div class="tool-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="tool-content">
                            <h4>Test de connexion</h4>
                            <p>Vérifie la connexion à la base de données et l'état des tables</p>
                            <a href="test-db.php" class="btn btn-info">
                                <i class="fas fa-stethoscope"></i>
                                Tester
                            </a>
                        </div>
                    </div>
                    
                    <div class="tool-card">
                        <div class="tool-icon">
                            <i class="fas fa-broom"></i>
                        </div>
                        <div class="tool-content">
                            <h4>Nettoyer les sessions</h4>
                            <p>Supprime les sessions expirées et les données temporaires</p>
                            <button class="btn btn-secondary" onclick="alert('Fonctionnalité à implémenter')">
                                <i class="fas fa-broom"></i>
                                Nettoyer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Onglet À propos -->
        <div id="about" class="tab-content">
            <div class="settings-section">
                <h3>À propos de l'interface d'administration</h3>
                
                <div class="about-info">
                    <div class="about-logo">
                        <i class="fas fa-industry"></i>
                        <h2><?php echo SITE_NAME; ?></h2>
                    </div>
                    
                    <div class="about-details">
                        <div class="detail-item">
                            <label>Version</label>
                            <span>1.0.0</span>
                        </div>
                        
                        <div class="detail-item">
                            <label>Développé avec</label>
                            <span>PHP, MySQL, HTML5, CSS3, JavaScript</span>
                        </div>
                        
                        <div class="detail-item">
                            <label>Fonctionnalités</label>
                            <ul>
                                <li>Gestion des actualités</li>
                                <li>Catalogue de formations</li>
                                <li>Planification d'événements</li>
                                <li>Emplois du temps</li>
                                <li>Gestion des utilisateurs</li>
                                <li>Configuration des secteurs</li>
                            </ul>
                        </div>
                        
                        <div class="detail-item">
                            <label>Utilisateur connecté</label>
                            <span>
                                <strong><?php echo htmlspecialchars($current_user['username']); ?></strong>
                                (<?php echo formatRole($current_user['role']); ?>)
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Gestion des onglets
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.settings-nav .nav-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Désactiver tous les onglets
            navItems.forEach(nav => nav.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Activer l'onglet sélectionné
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
