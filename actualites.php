<?php
$page_title = "Actualités";
$page_description = "Suivez toute l'actualité du Pôle Industrie : innovations, partenariats, événements et réussites de nos étudiants";

// Récupération des actualités depuis la base de données
$actualites = [];
$actualites_featured = [];
try {
    $pdo = getDBConnection();

    // Actualités à la une
    $stmt = $pdo->query("SELECT * FROM actualites WHERE statut = 'publie' AND featured = 1 ORDER BY date_publication DESC LIMIT 3");
    $actualites_featured = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Toutes les actualités
    $stmt = $pdo->query("SELECT * FROM actualites WHERE statut = 'publie' ORDER BY date_publication DESC");
    $actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // En cas d'erreur, on continue avec des tableaux vides
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1><i class="fas fa-newspaper"></i> Actualités</h1>
            <p>Découvrez les dernières nouvelles du Pôle Industrie : innovations, partenariats, événements et réussites de nos étudiants.</p>
        </div>
    </div>
</section>

<?php if (!empty($actualites_featured)): ?>
<!-- Actualités à la une -->
<section class="section">
    <div class="container">
        <div class="section-title">
            <h2><i class="fas fa-star"></i> À la une</h2>
            <p>Les actualités les plus importantes de notre établissement</p>
        </div>

        <div class="featured-news-grid">
            <?php foreach ($actualites_featured as $index => $actualite): ?>
                <article class="featured-news-card <?php echo $index === 0 ? 'featured-main' : ''; ?>">
                    <div class="news-image">
                        <div class="news-category <?php echo htmlspecialchars($actualite['categorie']); ?>">
                            <?php
                            $categories = [
                                'mecanique' => 'Mécanique',
                                'electricite' => 'Électricité',
                                'automobile' => 'Automobile',
                                'navale' => 'Naval',
                                'thermique' => 'Thermique',
                                'general' => 'Général'
                            ];
                            echo $categories[$actualite['categorie']] ?? 'Actualité';
                            ?>
                        </div>
                        <i class="fas fa-<?php
                            $icons = [
                                'mecanique' => 'cog',
                                'electricite' => 'bolt',
                                'automobile' => 'car',
                                'navale' => 'ship',
                                'thermique' => 'thermometer-half',
                                'general' => 'newspaper'
                            ];
                            echo $icons[$actualite['categorie']] ?? 'newspaper';
                        ?> news-icon"></i>
                    </div>
                    <div class="news-content">
                        <div class="news-meta">
                            <span class="news-date">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('d F Y', strtotime($actualite['date_publication'])); ?>
                            </span>
                        </div>
                        <h3><?php echo htmlspecialchars($actualite['titre']); ?></h3>
                        <p class="news-excerpt"><?php echo htmlspecialchars($actualite['extrait']); ?></p>
                        <a href="actualite-detail.php?id=<?php echo $actualite['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> Lire la suite
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>
<!-- Toutes les actualités -->
<section class="section" style="background: var(--light-gray);">
    <div class="container">
        <div class="section-title">
            <h2>Toutes nos actualités</h2>
            <p>Restez informé de toute l'actualité du Pôle Industrie</p>
        </div>

        <?php if (!empty($actualites)): ?>
            <div class="news-grid">
                <?php foreach ($actualites as $actualite): ?>
                    <article class="news-card">
                        <div class="news-image">
                            <div class="news-category <?php echo htmlspecialchars($actualite['categorie']); ?>">
                                <?php echo $categories[$actualite['categorie']] ?? 'Actualité'; ?>
                            </div>
                            <i class="fas fa-<?php echo $icons[$actualite['categorie']] ?? 'newspaper'; ?> news-icon"></i>
                        </div>
                        <div class="news-content">
                            <div class="news-meta">
                                <span class="news-date">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('d F Y', strtotime($actualite['date_publication'])); ?>
                                </span>
                                <?php if ($actualite['featured']): ?>
                                    <span class="news-featured">
                                        <i class="fas fa-star"></i> À la une
                                    </span>
                                <?php endif; ?>
                            </div>
                            <h3><?php echo htmlspecialchars($actualite['titre']); ?></h3>
                            <p class="news-excerpt"><?php echo htmlspecialchars($actualite['extrait']); ?></p>
                            <div class="news-actions">
                                <a href="actualite-detail.php?id=<?php echo $actualite['id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Lire la suite
                                </a>
                            </div>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="no-news">
                <i class="fas fa-newspaper"></i>
                <h3>Aucune actualité disponible</h3>
                <p>Les actualités seront bientôt disponibles. Revenez régulièrement pour ne rien manquer !</p>
                <a href="contact.php" class="btn btn-primary">Nous contacter</a>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Newsletter -->
<section class="section">
    <div class="container">
        <div class="newsletter-section">
            <div class="newsletter-content">
                <h2><i class="fas fa-envelope"></i> Restez informé</h2>
                <p>Inscrivez-vous à notre newsletter pour recevoir toutes nos actualités directement dans votre boîte mail.</p>
            </div>
            <div class="newsletter-form">
                <form action="#" method="POST" class="newsletter-signup">
                    <input type="email" name="email" placeholder="Votre adresse email" required>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> S'abonner
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
