<?php
// Configuration du site Pôle Industrie
define('SITE_NAME', 'Pôle Industrie');
define('SITE_DESCRIPTION', 'Centre de formation et d\'excellence industrielle');

// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'pole_industrie');
define('DB_USER', 'root');
define('DB_PASS', '');

// Connexion à la base de données
function getDBConnection() {
    try {
        // Essayer de se connecter à la base de données
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        // Si la base de données n'existe pas, essayer de la créer
        if ($e->getCode() == 1049) {
            try {
                // Connexion sans spécifier la base de données
                $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8", DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Créer la base de données
                $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8 COLLATE utf8_general_ci");

                // Se reconnecter à la nouvelle base de données
                $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Vérifier si les tables existent, sinon rediriger vers l'installation
                $result = $pdo->query("SHOW TABLES LIKE 'admin_users'");
                if ($result->rowCount() == 0) {
                    $current_path = $_SERVER['REQUEST_URI'];
                    if (strpos($current_path, 'admin/install.php') === false) {
                        $install_url = '/admin/install.php';
                        die("Tables non trouvées. <a href='" . $install_url . "'>Cliquez ici pour installer la base de données</a>");
                    }
                }

                return $pdo;
            } catch(PDOException $e2) {
                die("Erreur de connexion : " . $e2->getMessage() . "<br><a href='admin/install.php'>Cliquez ici pour installer la base de données</a>");
            }
        } else {
            die("Erreur de connexion : " . $e->getMessage() . "<br><a href='admin/install.php'>Cliquez ici pour installer la base de données</a>");
        }
    }
}

// Secteurs industriels avec leurs couleurs
$secteurs = [
    'mecanique' => [
        'nom' => 'Pôle Industrie de la Mécanique',
        'couleur' => '#1e3a8a',
        'description' => 'Formation aux métiers de la mécanique industrielle et de précision'
    ],
    'electricite' => [
        'nom' => 'Pôle Industrie de l\'Électricité',
        'couleur' => '#dc2626',
        'description' => 'Expertise en systèmes électriques et électroniques industriels'
    ],
    'genie_thermique' => [
        'nom' => 'Pôle Génie Thermique',
        'couleur' => '#7c3aed',
        'description' => 'Spécialisation en systèmes de chauffage et climatisation'
    ],
    'automobile' => [
        'nom' => 'Pôle Industrie de l\'Automobile',
        'couleur' => '#0ea5e9',
        'description' => 'Formation aux technologies automobiles modernes'
    ],
    'navale' => [
        'nom' => 'Pôle Industrie Navale',
        'couleur' => '#0d9488',
        'description' => 'Métiers de la construction et réparation navale'
    ],
    'metiers_industrie' => [
        'nom' => 'Pôle Métiers de l\'Industrie',
        'couleur' => '#16a34a',
        'description' => 'Formation transversale aux métiers industriels'
    ]
];

// Fonction pour obtenir la page actuelle
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF'], '.php');
}

// Fonction pour générer les liens de navigation
function isActivePage($page) {
    return getCurrentPage() === $page ? 'active' : '';
}
?>
