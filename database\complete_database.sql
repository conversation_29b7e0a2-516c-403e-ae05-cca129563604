-- =====================================================
-- BASE DE DONNÉES COMPLÈTE POUR LE PÔLE INDUSTRIE
-- =====================================================

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS pole_industrie CHARACTER SET utf8 COLLATE utf8_general_ci;
USE pole_industrie;

-- =====================================================
-- 1. TABLE DES UTILISATEURS ADMINISTRATEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- =====================================================
-- 2. TABLE DES SECTEURS
-- =====================================================
CREATE TABLE IF NOT EXISTS secteurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    couleur VARCHAR(7) DEFAULT '#1e3a8a',
    icone VARCHAR(50) DEFAULT 'fas fa-cog',
    ordre INT DEFAULT 0,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. TABLE DES FORMATIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duree VARCHAR(50),
    niveau ENUM('CAP', 'BTS', 'Licence Pro', 'Formation Continue', 'Formation Courte', 'Apprentissage') NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
    prix DECIMAL(10,2),
    places_disponibles INT DEFAULT 0,
    date_debut DATE,
    date_fin DATE,
    lieu VARCHAR(255),
    statut ENUM('active', 'inactive', 'complete') DEFAULT 'active',
    image VARCHAR(255),
    brochure VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 4. TABLE DES ACTUALITÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    extrait TEXT,
    image VARCHAR(255),
    categorie ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') DEFAULT 'general',
    statut ENUM('brouillon', 'publie', 'archive') DEFAULT 'brouillon',
    featured BOOLEAN DEFAULT FALSE,
    date_publication DATE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 5. TABLE DES ÉVÉNEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME,
    lieu VARCHAR(255),
    type_evenement ENUM('conference', 'salon', 'portes_ouvertes', 'formation', 'autre') DEFAULT 'autre',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    places_max INT,
    places_reservees INT DEFAULT 0,
    prix DECIMAL(10,2) DEFAULT 0,
    image VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 6. TABLE DES EMPLOIS DU TEMPS
-- =====================================================
CREATE TABLE IF NOT EXISTS emplois_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    date_cours DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    salle VARCHAR(100),
    formateur VARCHAR(255),
    type_cours ENUM('cours', 'tp', 'td', 'examen', 'stage') DEFAULT 'cours',
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 7. TABLE DES MESSAGES DE CONTACT
-- =====================================================
CREATE TABLE IF NOT EXISTS contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    sujet VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    secteur ENUM('mecanique', 'electricite', 'thermique', 'automobile', 'navale', 'general') NULL,
    statut ENUM('nouveau', 'lu', 'traite', 'archive') DEFAULT 'nouveau',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    reponse TEXT NULL,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 8. TABLE DES INSCRIPTIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS inscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT NOT NULL,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(50),
    adresse TEXT,
    date_naissance DATE,
    niveau_etude VARCHAR(100),
    experience_professionnelle TEXT,
    motivation TEXT,
    statut ENUM('en_attente', 'accepte', 'refuse', 'liste_attente') DEFAULT 'en_attente',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    traite_par INT NULL,
    commentaires TEXT,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (traite_par) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- =====================================================
-- 9. TABLE DES PARAMÈTRES DU SITE
-- =====================================================
CREATE TABLE IF NOT EXISTS parametres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cle VARCHAR(100) UNIQUE NOT NULL,
    valeur TEXT,
    description TEXT,
    type ENUM('text', 'textarea', 'number', 'boolean', 'email', 'url') DEFAULT 'text',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- INSERTION DES DONNÉES PAR DÉFAUT
-- =====================================================

-- Utilisateur admin par défaut
INSERT IGNORE INTO admin_users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$8K1p/wJIiU.5keowvUuYQOuiuiuqiUYqyMxdnQX9.vGA/l9dcCCO6', 'admin');

-- Secteurs par défaut
INSERT IGNORE INTO secteurs (nom, slug, description, couleur, icone, ordre) VALUES
('Pôle Industrie de la Mécanique', 'mecanique', 'Formation aux métiers de la mécanique industrielle et de précision', '#1e3a8a', 'fas fa-cog', 1),
('Pôle Industrie de l\'Électricité', 'electricite', 'Expertise en systèmes électriques et électroniques industriels', '#dc2626', 'fas fa-bolt', 2),
('Pôle Génie Thermique', 'thermique', 'Spécialisation en systèmes de chauffage et climatisation', '#7c3aed', 'fas fa-thermometer-half', 3),
('Pôle Industrie de l\'Automobile', 'automobile', 'Formation aux technologies automobiles modernes', '#0ea5e9', 'fas fa-car', 4),
('Pôle Industrie Navale', 'navale', 'Métiers de la construction et réparation navale', '#0d9488', 'fas fa-ship', 5),
('Pôle Métiers de l\'Industrie', 'general', 'Formation transversale aux métiers industriels', '#16a34a', 'fas fa-tools', 6);

-- Paramètres du site
INSERT IGNORE INTO parametres (cle, valeur, description, type) VALUES
('site_name', 'Pôle Industrie', 'Nom du site', 'text'),
('site_description', 'Centre de formation et d\'excellence industrielle', 'Description du site', 'textarea'),
('contact_email', '<EMAIL>', 'Email de contact principal', 'email'),
('contact_phone', '+33 1 23 45 67 89', 'Téléphone de contact', 'text'),
('contact_address', '123 Avenue de l\'Industrie, 75000 Paris, France', 'Adresse de contact', 'textarea'),
('facebook_url', '#', 'URL Facebook', 'url'),
('linkedin_url', '#', 'URL LinkedIn', 'url'),
('twitter_url', '#', 'URL Twitter', 'url'),
('youtube_url', '#', 'URL YouTube', 'url');

-- =====================================================
-- DONNÉES D'EXEMPLE POUR LES FORMATIONS
-- =====================================================
INSERT IGNORE INTO formations (nom, description, duree, niveau, secteur, prix, places_disponibles, date_debut, date_fin, lieu, statut, created_by) VALUES
('CAP Maintenance des Véhicules', 'Formation complète en maintenance automobile avec pratique sur véhicules récents', '2 ans', 'CAP', 'automobile', 0.00, 20, '2024-09-01', '2026-06-30', 'Atelier Automobile', 'active', 1),
('BTS Électrotechnique', 'Formation en systèmes électriques industriels et domotique', '2 ans', 'BTS', 'electricite', 0.00, 15, '2024-09-01', '2026-06-30', 'Laboratoire Électricité', 'active', 1),
('Formation Continue Soudage', 'Perfectionnement en techniques de soudage industriel', '3 mois', 'Formation Continue', 'mecanique', 2500.00, 12, '2024-03-01', '2024-05-31', 'Atelier Mécanique', 'active', 1),
('Licence Pro Génie Thermique', 'Spécialisation en systèmes de chauffage et climatisation', '1 an', 'Licence Pro', 'thermique', 0.00, 25, '2024-09-01', '2025-06-30', 'Campus Principal', 'active', 1),
('Formation Courte Hydraulique', 'Initiation aux systèmes hydrauliques industriels', '1 mois', 'Formation Courte', 'mecanique', 1200.00, 10, '2024-02-01', '2024-02-29', 'Atelier Hydraulique', 'active', 1),
('Apprentissage Construction Navale', 'Formation en alternance aux métiers de la construction navale', '3 ans', 'Apprentissage', 'navale', 0.00, 18, '2024-09-01', '2027-06-30', 'Chantier Naval Partenaire', 'active', 1);

-- =====================================================
-- DONNÉES D'EXEMPLE POUR LES ACTUALITÉS
-- =====================================================
INSERT IGNORE INTO actualites (titre, contenu, extrait, categorie, statut, featured, date_publication, created_by) VALUES
('Nouvelle Formation en Robotique Industrielle', 'Le Pôle Industrie lance une nouvelle formation dédiée à la robotique industrielle. Cette formation de 6 mois permettra aux participants de maîtriser les technologies robotiques les plus avancées...', 'Découvrez notre nouvelle formation en robotique industrielle, une opportunité unique de se former aux technologies de demain.', 'mecanique', 'publie', TRUE, '2024-01-15', 1),
('Partenariat avec Renault', 'Nous sommes fiers d\'annoncer notre nouveau partenariat avec Renault pour la formation de techniciens automobiles. Ce partenariat permettra à nos étudiants d\'accéder à des équipements de pointe...', 'Un nouveau partenariat stratégique avec Renault pour renforcer nos formations automobiles.', 'automobile', 'publie', TRUE, '2024-01-10', 1),
('Journée Portes Ouvertes 2024', 'Venez découvrir nos formations lors de notre journée portes ouvertes le 15 mars 2024. Au programme : visites des ateliers, démonstrations, rencontres avec les formateurs...', 'Rejoignez-nous pour notre journée portes ouvertes et découvrez l\'excellence de nos formations.', 'general', 'publie', FALSE, '2024-01-05', 1),
('Innovation en Génie Thermique', 'Nos étudiants en génie thermique ont développé un système de chauffage innovant qui réduit la consommation énergétique de 30%. Ce projet sera présenté au salon de l\'innovation...', 'Un projet étudiant révolutionnaire en génie thermique remporte un prix d\'innovation.', 'thermique', 'publie', FALSE, '2024-01-01', 1);

-- =====================================================
-- DONNÉES D'EXEMPLE POUR LES ÉVÉNEMENTS
-- =====================================================
INSERT IGNORE INTO evenements (titre, description, date_debut, date_fin, lieu, type_evenement, statut, places_max, prix, created_by) VALUES
('Journée Portes Ouvertes 2024', 'Découvrez nos formations, visitez nos ateliers et rencontrez nos équipes pédagogiques', '2024-03-15 09:00:00', '2024-03-15 17:00:00', 'Campus Principal', 'portes_ouvertes', 'planifie', 200, 0.00, 1),
('Salon de l\'Industrie 4.0', 'Participation au salon national de l\'industrie 4.0 avec présentation de nos formations', '2024-04-10 08:00:00', '2024-04-12 18:00:00', 'Parc des Expositions Paris', 'salon', 'planifie', 50, 0.00, 1),
('Conférence : L\'Avenir de l\'Automobile Électrique', 'Conférence animée par des experts du secteur automobile sur les enjeux de la mobilité électrique', '2024-02-20 14:00:00', '2024-02-20 17:00:00', 'Amphithéâtre A', 'conference', 'planifie', 100, 0.00, 1),
('Formation : Sécurité en Milieu Industriel', 'Session de formation obligatoire sur la sécurité en milieu industriel', '2024-02-05 08:00:00', '2024-02-05 12:00:00', 'Salle de Formation B', 'formation', 'planifie', 30, 0.00, 1);

-- =====================================================
-- DONNÉES D'EXEMPLE POUR LES EMPLOIS DU TEMPS
-- =====================================================
INSERT IGNORE INTO emplois_temps (formation_id, titre, description, date_cours, heure_debut, heure_fin, salle, formateur, type_cours, created_by) VALUES
(1, 'Diagnostic Électronique Automobile', 'Utilisation des outils de diagnostic OBD', '2024-02-05', '08:00:00', '12:00:00', 'Atelier Auto A', 'M. Dubois', 'tp', 1),
(1, 'Mécanique Générale', 'Cours théorique sur les systèmes mécaniques', '2024-02-05', '14:00:00', '16:00:00', 'Salle 101', 'Mme Martin', 'cours', 1),
(2, 'Électrotechnique Industrielle', 'Étude des moteurs électriques industriels', '2024-02-06', '08:00:00', '10:00:00', 'Labo Élec 1', 'M. Leroy', 'cours', 1),
(2, 'TP Automatismes', 'Travaux pratiques sur automates programmables', '2024-02-06', '10:30:00', '12:30:00', 'Labo Élec 2', 'M. Leroy', 'tp', 1),
(3, 'Techniques de Soudage TIG', 'Perfectionnement soudage TIG sur acier inox', '2024-02-07', '08:00:00', '12:00:00', 'Atelier Soudage', 'M. Rousseau', 'tp', 1),
(4, 'Thermodynamique Appliquée', 'Calculs thermodynamiques pour systèmes HVAC', '2024-02-08', '09:00:00', '11:00:00', 'Salle 205', 'Mme Petit', 'cours', 1);
