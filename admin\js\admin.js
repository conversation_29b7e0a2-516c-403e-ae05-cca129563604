// JavaScript pour l'interface d'administration

document.addEventListener('DOMContentLoaded', function() {
    
    // Gestion des alertes auto-dismiss
    const alerts = document.querySelectorAll('.alert[data-dismiss="auto"]');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
    
    // Confirmation de suppression
    const deleteButtons = document.querySelectorAll('[data-action="delete"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const itemName = this.getAttribute('data-item') || 'cet élément';
            const confirmMessage = `Êtes-vous sûr de vouloir supprimer ${itemName} ? Cette action est irréversible.`;
            
            if (confirm(confirmMessage)) {
                // Si c'est un lien, suivre le lien
                if (this.tagName === 'A') {
                    window.location.href = this.href;
                }
                // Si c'est un bouton dans un formulaire, soumettre le formulaire
                else if (this.form) {
                    this.form.submit();
                }
            }
        });
    });
    
    // Gestion des filtres de statut
    const statusFilters = document.querySelectorAll('.status-filter');
    statusFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const status = this.value;
            const rows = document.querySelectorAll('.data-row');
            
            rows.forEach(row => {
                if (status === '' || row.getAttribute('data-status') === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
    
    // Sélection multiple
    const selectAllCheckbox = document.getElementById('select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleBulkActions();
        });
    }
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
            toggleBulkActions();
        });
    });
    
    function toggleBulkActions() {
        const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
        if (bulkActions) {
            bulkActions.style.display = checkedCount > 0 ? 'block' : 'none';
        }
    }
    
    // Prévisualisation d'images
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    imageInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            const preview = document.getElementById(this.getAttribute('data-preview'));
            
            if (file && preview) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    });
    
    // Éditeur de texte simple
    const textareas = document.querySelectorAll('textarea.rich-editor');
    textareas.forEach(textarea => {
        // Ajouter des boutons de formatage basiques
        const toolbar = document.createElement('div');
        toolbar.className = 'editor-toolbar';
        toolbar.innerHTML = `
            <button type="button" data-action="bold"><i class="fas fa-bold"></i></button>
            <button type="button" data-action="italic"><i class="fas fa-italic"></i></button>
            <button type="button" data-action="underline"><i class="fas fa-underline"></i></button>
            <button type="button" data-action="link"><i class="fas fa-link"></i></button>
        `;
        
        textarea.parentNode.insertBefore(toolbar, textarea);
        
        // Gestion des actions de formatage
        toolbar.addEventListener('click', function(e) {
            if (e.target.closest('button')) {
                e.preventDefault();
                const action = e.target.closest('button').getAttribute('data-action');
                formatText(textarea, action);
            }
        });
    });
    
    function formatText(textarea, action) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        let replacement = selectedText;
        
        switch (action) {
            case 'bold':
                replacement = `**${selectedText}**`;
                break;
            case 'italic':
                replacement = `*${selectedText}*`;
                break;
            case 'underline':
                replacement = `<u>${selectedText}</u>`;
                break;
            case 'link':
                const url = prompt('URL du lien:');
                if (url) {
                    replacement = `[${selectedText || 'Texte du lien'}](${url})`;
                }
                break;
        }
        
        textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start, start + replacement.length);
    }
    
    // Sauvegarde automatique des brouillons
    const forms = document.querySelectorAll('form[data-autosave]');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', debounce(() => {
                saveDraft(form);
            }, 2000));
        });
        
        // Charger le brouillon au chargement de la page
        loadDraft(form);
    });
    
    function saveDraft(form) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        const draftKey = `draft_${form.getAttribute('data-autosave')}`;
        localStorage.setItem(draftKey, JSON.stringify(data));
        
        // Afficher un indicateur de sauvegarde
        showSaveIndicator();
    }
    
    function loadDraft(form) {
        const draftKey = `draft_${form.getAttribute('data-autosave')}`;
        const savedData = localStorage.getItem(draftKey);
        
        if (savedData) {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input && input.value === '') {
                    input.value = data[key];
                }
            });
        }
    }
    
    function showSaveIndicator() {
        const indicator = document.getElementById('save-indicator') || createSaveIndicator();
        indicator.textContent = 'Brouillon sauvegardé';
        indicator.style.opacity = '1';
        
        setTimeout(() => {
            indicator.style.opacity = '0';
        }, 2000);
    }
    
    function createSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'save-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--admin-success);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        `;
        document.body.appendChild(indicator);
        return indicator;
    }
    
    // Fonction utilitaire debounce
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Gestion des onglets
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Désactiver tous les onglets
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Activer l'onglet sélectionné
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
    
});

// Fonction globale pour afficher des notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
        <span>${message}</span>
        <button type="button" class="notification-close">&times;</button>
    `;
    
    document.body.appendChild(notification);
    
    // Afficher la notification
    setTimeout(() => notification.classList.add('show'), 100);
    
    // Masquer automatiquement après 5 secondes
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
    
    // Bouton de fermeture
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    });
}
