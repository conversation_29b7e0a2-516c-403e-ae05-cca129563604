<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification
requireLogin();

$current_user = getCurrentUser();
$page_title = "Tableau de bord";

// Récupérer les statistiques
try {
    $pdo = getDBConnection();
    
    // Compter les actualités
    $stmt = $pdo->query("SELECT COUNT(*) as total, 
                         SUM(CASE WHEN statut = 'publie' THEN 1 ELSE 0 END) as publies,
                         SUM(CASE WHEN statut = 'brouillon' THEN 1 ELSE 0 END) as brouillons
                         FROM actualites");
    $stats_actualites = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Compter les formations
    $stmt = $pdo->query("SELECT COUNT(*) as total,
                         SUM(CASE WHEN statut = 'active' THEN 1 ELSE 0 END) as actives
                         FROM formations");
    $stats_formations = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Compter les événements
    $stmt = $pdo->query("SELECT COUNT(*) as total,
                         SUM(CASE WHEN statut = 'planifie' THEN 1 ELSE 0 END) as planifies
                         FROM evenements");
    $stats_evenements = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Compter les utilisateurs
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM admin_users");
    $stats_users = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Dernières actualités
    $stmt = $pdo->query("SELECT titre, statut, created_at FROM actualites ORDER BY created_at DESC LIMIT 5");
    $dernieres_actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = "Erreur lors du chargement des statistiques";
}

include 'includes/header.php';
?>

<div class="dashboard">
    <div class="dashboard-header">
        <h1>Tableau de bord</h1>
        <p>Bienvenue, <?php echo htmlspecialchars($current_user['username']); ?> !</p>
    </div>
    
    <!-- Statistiques -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon actualites">
                <i class="fas fa-newspaper"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats_actualites['total'] ?? 0; ?></h3>
                <p>Actualités</p>
                <small><?php echo $stats_actualites['publies'] ?? 0; ?> publiées, <?php echo $stats_actualites['brouillons'] ?? 0; ?> brouillons</small>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon formations">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats_formations['total'] ?? 0; ?></h3>
                <p>Formations</p>
                <small><?php echo $stats_formations['actives'] ?? 0; ?> actives</small>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon evenements">
                <i class="fas fa-calendar"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats_evenements['total'] ?? 0; ?></h3>
                <p>Événements</p>
                <small><?php echo $stats_evenements['planifies'] ?? 0; ?> planifiés</small>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon users">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats_users['total'] ?? 0; ?></h3>
                <p>Utilisateurs</p>
                <small>Administrateurs</small>
            </div>
        </div>
    </div>
    
    <!-- Actions rapides -->
    <div class="quick-actions">
        <h2>Actions rapides</h2>
        <div class="actions-grid">
            <a href="actualites.php?action=add" class="action-card">
                <i class="fas fa-plus"></i>
                <span>Ajouter une actualité</span>
            </a>
            <a href="formations.php?action=add" class="action-card">
                <i class="fas fa-plus"></i>
                <span>Créer une formation</span>
            </a>
            <a href="evenements.php?action=add" class="action-card">
                <i class="fas fa-plus"></i>
                <span>Planifier un événement</span>
            </a>
            <a href="emplois-temps.php?action=add" class="action-card">
                <i class="fas fa-plus"></i>
                <span>Ajouter un cours</span>
            </a>
        </div>
    </div>
    
    <!-- Activité récente -->
    <div class="recent-activity">
        <h2>Activité récente</h2>
        <div class="activity-list">
            <?php if (!empty($dernieres_actualites)): ?>
                <?php foreach ($dernieres_actualites as $actualite): ?>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <div class="activity-content">
                            <h4><?php echo htmlspecialchars($actualite['titre']); ?></h4>
                            <p>Actualité <?php echo $actualite['statut']; ?></p>
                            <small><?php echo date('d/m/Y H:i', strtotime($actualite['created_at'])); ?></small>
                        </div>
                        <div class="activity-status">
                            <span class="status-badge <?php echo $actualite['statut']; ?>">
                                <?php echo ucfirst($actualite['statut']); ?>
                            </span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="no-activity">
                    <i class="fas fa-info-circle"></i>
                    <p>Aucune activité récente</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
