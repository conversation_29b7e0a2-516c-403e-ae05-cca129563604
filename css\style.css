/* Variables CSS pour les couleurs industrielles */
:root {
    --primary-blue: #1e3a8a;
    --electric-red: #dc2626;
    --thermal-purple: #7c3aed;
    --auto-blue: #0ea5e9;
    --naval-teal: #0d9488;
    --industry-green: #16a34a;
    --dark-gray: #1f2937;
    --light-gray: #f8fafc;
    --white: #ffffff;
    --gradient-industrial: linear-gradient(135deg, var(--primary-blue), var(--naval-teal));
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--light-gray);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.main-header {
    background: var(--gradient-industrial);
    color: var(--white);
    padding: 1rem 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.2rem;
}

.logo p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    color: var(--white);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.main-nav a:hover,
.main-nav a.active {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--white);
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: var(--gradient-industrial);
    color: var(--white);
    padding: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-blue);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Secteurs Grid */
.secteurs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 4rem 0;
}

.secteur-card {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.secteur-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-blue);
    transition: all 0.3s ease;
}

.secteur-card.mecanique::before { background: var(--primary-blue); }
.secteur-card.electricite::before { background: var(--electric-red); }
.secteur-card.genie-thermique::before { background: var(--thermal-purple); }
.secteur-card.automobile::before { background: var(--auto-blue); }
.secteur-card.navale::before { background: var(--naval-teal); }
.secteur-card.metiers-industrie::before { background: var(--industry-green); }

.secteur-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0,0,0,0.15);
}

.secteur-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

.secteur-card.mecanique .secteur-icon { color: var(--primary-blue); }
.secteur-card.electricite .secteur-icon { color: var(--electric-red); }
.secteur-card.genie-thermique .secteur-icon { color: var(--thermal-purple); }
.secteur-card.automobile .secteur-icon { color: var(--auto-blue); }
.secteur-card.navale .secteur-icon { color: var(--naval-teal); }
.secteur-card.metiers-industrie .secteur-icon { color: var(--industry-green); }

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Footer */
.main-footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--auto-blue);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-blue);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    background: var(--auto-blue);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 1rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.contact-info p {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Sections communes */
.section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-industrial);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

/* Stats Section */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #6b7280;
}

/* Mobile Navigation */
.mobile-nav {
    display: none;
    background: var(--dark-gray);
    position: fixed;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100vh;
    z-index: 9999;
    transition: left 0.3s ease;
    padding-top: 4rem;
}

.mobile-nav.active {
    left: 0;
}

.mobile-nav ul {
    list-style: none;
    padding: 2rem;
}

.mobile-nav li {
    margin-bottom: 1rem;
}

.mobile-nav a {
    color: var(--white);
    text-decoration: none;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.mobile-nav a:hover {
    background: var(--primary-blue);
}

/* Secteur Detail Pages */
.secteur-detail {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.secteur-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.secteur-header .secteur-icon {
    font-size: 4rem;
}

.secteur-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.secteur-subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    margin: 0;
}

.secteur-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.secteur-description h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-gray);
}

.secteur-description ul {
    list-style: none;
    padding: 0;
}

.secteur-description li {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
}

.secteur-description li i {
    color: var(--industry-green);
    font-size: 1rem;
}

.secteur-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Formulaires */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-blue);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
    border-color: var(--electric-red);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: var(--white);
    font-weight: 600;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: var(--industry-green);
}

.notification-error {
    background: var(--electric-red);
}

.notification-info {
    background: var(--auto-blue);
}

/* Formations */
.formation-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    border: 2px solid var(--primary-blue);
    background: transparent;
    color: var(--primary-blue);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-blue);
    color: var(--white);
}

.formations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.formation-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.formation-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0,0,0,0.15);
}

.formation-image {
    position: relative;
    background: var(--gradient-industrial);
    padding: 2rem;
    text-align: center;
    color: var(--white);
}

.formation-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255,255,255,0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.formation-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.formation-content {
    padding: 1.5rem;
}

.formation-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-gray);
}

.formation-description {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.formation-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.formation-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.formation-detail i {
    color: var(--primary-blue);
    width: 16px;
}

.formation-actions {
    display: flex;
    gap: 1rem;
}

.formation-actions .btn {
    flex: 1;
    justify-content: center;
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
}

/* Info Cards */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.info-card h3 {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.info-card h3 i {
    color: var(--primary-blue);
}

.info-card ul {
    list-style: none;
    padding: 0;
}

.info-card li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
    color: #6b7280;
}

.info-card li:last-child {
    border-bottom: none;
}

/* Actualités */
.actualites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.actualite-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.actualite-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.actualite-card.featured {
    grid-column: span 2;
}

.actualite-image {
    position: relative;
    background: var(--gradient-industrial);
    padding: 2rem;
    text-align: center;
    color: var(--white);
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.actualite-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--electric-red);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.actualite-icon {
    font-size: 2.5rem;
}

.actualite-content {
    padding: 1.5rem;
}

.actualite-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.actualite-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.actualite-category {
    padding: 0.2rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--white);
}

.actualite-category.mecanique { background: var(--primary-blue); }
.actualite-category.electricite { background: var(--electric-red); }
.actualite-category.automobile { background: var(--auto-blue); }
.actualite-category.navale { background: var(--naval-teal); }
.actualite-category.thermique { background: var(--thermal-purple); }

.actualite-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-gray);
    line-height: 1.4;
}

.actualite-content p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Highlight Section */
.highlight-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
    background: var(--white);
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.highlight-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.highlight-content p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.highlight-image {
    text-align: center;
    color: var(--primary-blue);
}

.highlight-icon {
    font-size: 8rem;
}

/* Projets */
.projets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.projet-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.projet-card:hover {
    transform: translateY(-5px);
}

.projet-image {
    background: var(--gradient-industrial);
    padding: 2rem;
    text-align: center;
    color: var(--white);
}

.projet-icon {
    font-size: 3rem;
}

.projet-content {
    padding: 1.5rem;
}

.projet-content h3 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-gray);
}

.projet-content p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.projet-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: var(--light-gray);
    color: var(--dark-gray);
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Newsletter */
.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-signup .form-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 0;
}

.newsletter-signup input {
    flex: 1;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: var(--white);
}

.newsletter-signup input::placeholder {
    color: rgba(255,255,255,0.7);
}

.newsletter-signup button {
    white-space: nowrap;
}
