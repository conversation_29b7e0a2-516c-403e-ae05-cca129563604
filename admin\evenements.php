<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification
requireLogin();

$page_title = "Gestion des événements";
$current_user = getCurrentUser();

// Gestion des actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

try {
    $pdo = getDBConnection();
    
    // Traitement des actions POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['bulk_action']) && isset($_POST['selected_items'])) {
            // Actions en lot
            $bulk_action = $_POST['bulk_action'];
            $selected_items = $_POST['selected_items'];
            
            foreach ($selected_items as $item_id) {
                switch ($bulk_action) {
                    case 'planifie':
                        $stmt = $pdo->prepare("UPDATE evenements SET statut = 'planifie' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'annule':
                        $stmt = $pdo->prepare("UPDATE evenements SET statut = 'annule' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'delete':
                        $stmt = $pdo->prepare("DELETE FROM evenements WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                }
            }
            $message = "Actions appliquées avec succès";
        }
        
        // Sauvegarde d'événement
        if (isset($_POST['save_evenement'])) {
            $titre = trim($_POST['titre'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $date_debut = $_POST['date_debut'] ?? '';
            $heure_debut = $_POST['heure_debut'] ?? '';
            $date_fin = $_POST['date_fin'] ?? '';
            $heure_fin = $_POST['heure_fin'] ?? '';
            $lieu = trim($_POST['lieu'] ?? '');
            $type_evenement = $_POST['type_evenement'] ?? 'autre';
            $statut = $_POST['statut'] ?? 'planifie';
            $places_max = $_POST['places_max'] ?? null;
            $prix = $_POST['prix'] ?? 0;
            
            // Combiner date et heure
            $datetime_debut = $date_debut . ' ' . $heure_debut;
            $datetime_fin = '';
            if ($date_fin && $heure_fin) {
                $datetime_fin = $date_fin . ' ' . $heure_fin;
            }
            
            if (empty($titre) || empty($description) || empty($date_debut) || empty($heure_debut)) {
                $error = "Le titre, la description, la date et l'heure de début sont obligatoires";
            } else {
                try {
                    if ($action === 'add') {
                        $stmt = $pdo->prepare("
                            INSERT INTO evenements (titre, description, date_debut, date_fin, lieu, type_evenement, statut, places_max, prix, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$titre, $description, $datetime_debut, $datetime_fin ?: null, $lieu, $type_evenement, $statut, $places_max, $prix, $current_user['id']]);
                        $message = "Événement créé avec succès";
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE evenements 
                            SET titre = ?, description = ?, date_debut = ?, date_fin = ?, lieu = ?, type_evenement = ?, statut = ?, places_max = ?, prix = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$titre, $description, $datetime_debut, $datetime_fin ?: null, $lieu, $type_evenement, $statut, $places_max, $prix, $id]);
                        $message = "Événement modifié avec succès";
                    }
                    
                    header("Location: evenements.php?message=" . urlencode($message));
                    exit;
                    
                } catch (PDOException $e) {
                    $error = "Erreur lors de la sauvegarde : " . $e->getMessage();
                }
            }
        }
    }
    
    // Traitement des actions GET
    if ($action === 'delete' && $id) {
        $stmt = $pdo->prepare("DELETE FROM evenements WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Événement supprimé avec succès";
        $action = 'list';
    }
    
    // Récupération des événements pour la liste
    if ($action === 'list') {
        $filter_status = $_GET['status'] ?? '';
        $filter_type = $_GET['type'] ?? '';
        $search = $_GET['search'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if ($filter_status) {
            $where_conditions[] = "statut = ?";
            $params[] = $filter_status;
        }
        
        if ($filter_type) {
            $where_conditions[] = "type_evenement = ?";
            $params[] = $filter_type;
        }
        
        if ($search) {
            $where_conditions[] = "(titre LIKE ? OR description LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $stmt = $pdo->prepare("
            SELECT e.*, u.username as created_by_name 
            FROM evenements e 
            LEFT JOIN admin_users u ON e.created_by = u.id 
            $where_clause 
            ORDER BY e.date_debut ASC
        ");
        $stmt->execute($params);
        $evenements = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = "Erreur de base de données : " . $e->getMessage();
}

include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success" data-dismiss="auto">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <div class="page-header">
        <div class="page-title">
            <h1>Gestion des événements</h1>
            <p>Planifiez et gérez les événements de votre établissement</p>
        </div>
        <div class="page-actions">
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Ajouter un événement
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="filters-bar">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text" name="search" placeholder="Rechercher..." 
                       value="<?php echo htmlspecialchars($search ?? ''); ?>">
            </div>
            
            <div class="filter-group">
                <select name="status">
                    <option value="">Tous les statuts</option>
                    <option value="planifie" <?php echo ($filter_status === 'planifie') ? 'selected' : ''; ?>>Planifié</option>
                    <option value="en_cours" <?php echo ($filter_status === 'en_cours') ? 'selected' : ''; ?>>En cours</option>
                    <option value="termine" <?php echo ($filter_status === 'termine') ? 'selected' : ''; ?>>Terminé</option>
                    <option value="annule" <?php echo ($filter_status === 'annule') ? 'selected' : ''; ?>>Annulé</option>
                </select>
            </div>
            
            <div class="filter-group">
                <select name="type">
                    <option value="">Tous les types</option>
                    <option value="conference" <?php echo ($filter_type === 'conference') ? 'selected' : ''; ?>>Conférence</option>
                    <option value="salon" <?php echo ($filter_type === 'salon') ? 'selected' : ''; ?>>Salon</option>
                    <option value="portes_ouvertes" <?php echo ($filter_type === 'portes_ouvertes') ? 'selected' : ''; ?>>Portes ouvertes</option>
                    <option value="formation" <?php echo ($filter_type === 'formation') ? 'selected' : ''; ?>>Formation</option>
                    <option value="autre" <?php echo ($filter_type === 'autre') ? 'selected' : ''; ?>>Autre</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-search"></i>
                Filtrer
            </button>
        </form>
    </div>
    
    <!-- Actions en lot -->
    <form method="POST" id="bulk-form">
        <div class="bulk-actions" style="display: none;">
            <select name="bulk_action">
                <option value="">Actions en lot</option>
                <option value="planifie">Marquer comme planifié</option>
                <option value="annule">Annuler</option>
                <option value="delete">Supprimer</option>
            </select>
            <button type="submit" class="btn btn-secondary">Appliquer</button>
        </div>
        
        <!-- Tableau des événements -->
        <div class="data-table">
            <table>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all"></th>
                        <th>Événement</th>
                        <th>Date/Heure</th>
                        <th>Type</th>
                        <th>Lieu</th>
                        <th>Places</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($evenements)): ?>
                        <tr>
                            <td colspan="8" class="no-data">
                                <i class="fas fa-calendar"></i>
                                <p>Aucun événement trouvé</p>
                                <a href="?action=add" class="btn btn-primary">Créer le premier événement</a>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($evenements as $evenement): ?>
                            <tr class="data-row" data-status="<?php echo $evenement['statut']; ?>">
                                <td>
                                    <input type="checkbox" name="selected_items[]" value="<?php echo $evenement['id']; ?>" class="item-checkbox">
                                </td>
                                <td>
                                    <div class="item-title">
                                        <strong><?php echo htmlspecialchars($evenement['titre']); ?></strong>
                                        <div class="item-meta">
                                            <?php if ($evenement['prix'] > 0): ?>
                                                <?php echo number_format($evenement['prix'], 2); ?>€
                                            <?php else: ?>
                                                Gratuit
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        <strong><?php echo date('d/m/Y', strtotime($evenement['date_debut'])); ?></strong><br>
                                        <small><?php echo date('H:i', strtotime($evenement['date_debut'])); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="type-badge <?php echo $evenement['type_evenement']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $evenement['type_evenement'])); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($evenement['lieu'] ?? 'Non défini'); ?></td>
                                <td>
                                    <?php if ($evenement['places_max']): ?>
                                        <span class="places-count">
                                            <?php echo ($evenement['places_reservees'] ?? 0); ?>/<?php echo $evenement['places_max']; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">Illimité</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $evenement['statut']; ?>">
                                        <?php echo ucfirst($evenement['statut']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=edit&id=<?php echo $evenement['id']; ?>" class="btn-icon" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="?action=delete&id=<?php echo $evenement['id']; ?>" class="btn-icon btn-danger" 
                                           data-action="delete" data-item="<?php echo htmlspecialchars($evenement['titre']); ?>" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </form>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <?php
    $evenement = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM evenements WHERE id = ?");
        $stmt->execute([$id]);
        $evenement = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$evenement) {
            $error = "Événement non trouvé";
            $action = 'list';
        }
    }
    ?>

    <div class="page-header">
        <div class="page-title">
            <h1><?php echo $action === 'add' ? 'Ajouter' : 'Modifier'; ?> un événement</h1>
            <p>Planifiez un nouvel événement pour votre établissement</p>
        </div>
        <div class="page-actions">
            <a href="evenements.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Retour à la liste
            </a>
        </div>
    </div>

    <form method="POST" class="form-container" data-autosave="evenement_<?php echo $id ?? 'new'; ?>">
        <div class="form-grid">
            <div class="form-main">
                <div class="form-section">
                    <h3>Informations générales</h3>

                    <div class="form-group">
                        <label for="titre">Titre de l'événement *</label>
                        <input type="text" id="titre" name="titre" required
                               value="<?php echo htmlspecialchars($evenement['titre'] ?? ''); ?>"
                               placeholder="Ex: Journée Portes Ouvertes 2024">
                    </div>

                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" rows="6" required class="rich-editor"
                                  placeholder="Description détaillée de l'événement, programme, intervenants..."><?php echo htmlspecialchars($evenement['description'] ?? ''); ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="lieu">Lieu</label>
                        <input type="text" id="lieu" name="lieu"
                               value="<?php echo htmlspecialchars($evenement['lieu'] ?? ''); ?>"
                               placeholder="Ex: Amphithéâtre principal, Campus">
                    </div>
                </div>

                <div class="form-section">
                    <h3>Dates et horaires</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="date_debut">Date de début *</label>
                            <input type="date" id="date_debut" name="date_debut" required
                                   value="<?php echo $evenement ? date('Y-m-d', strtotime($evenement['date_debut'])) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="heure_debut">Heure de début *</label>
                            <input type="time" id="heure_debut" name="heure_debut" required
                                   value="<?php echo $evenement ? date('H:i', strtotime($evenement['date_debut'])) : ''; ?>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="date_fin">Date de fin</label>
                            <input type="date" id="date_fin" name="date_fin"
                                   value="<?php echo $evenement && $evenement['date_fin'] ? date('Y-m-d', strtotime($evenement['date_fin'])) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="heure_fin">Heure de fin</label>
                            <input type="time" id="heure_fin" name="heure_fin"
                                   value="<?php echo $evenement && $evenement['date_fin'] ? date('H:i', strtotime($evenement['date_fin'])) : ''; ?>">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-sidebar">
                <div class="form-section">
                    <h3>Type et statut</h3>

                    <div class="form-group">
                        <label for="type_evenement">Type d'événement</label>
                        <select id="type_evenement" name="type_evenement">
                            <option value="conference" <?php echo ($evenement['type_evenement'] ?? '') === 'conference' ? 'selected' : ''; ?>>Conférence</option>
                            <option value="salon" <?php echo ($evenement['type_evenement'] ?? '') === 'salon' ? 'selected' : ''; ?>>Salon</option>
                            <option value="portes_ouvertes" <?php echo ($evenement['type_evenement'] ?? '') === 'portes_ouvertes' ? 'selected' : ''; ?>>Portes ouvertes</option>
                            <option value="formation" <?php echo ($evenement['type_evenement'] ?? '') === 'formation' ? 'selected' : ''; ?>>Formation</option>
                            <option value="autre" <?php echo ($evenement['type_evenement'] ?? 'autre') === 'autre' ? 'selected' : ''; ?>>Autre</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="statut">Statut</label>
                        <select id="statut" name="statut">
                            <option value="planifie" <?php echo ($evenement['statut'] ?? 'planifie') === 'planifie' ? 'selected' : ''; ?>>Planifié</option>
                            <option value="en_cours" <?php echo ($evenement['statut'] ?? '') === 'en_cours' ? 'selected' : ''; ?>>En cours</option>
                            <option value="termine" <?php echo ($evenement['statut'] ?? '') === 'termine' ? 'selected' : ''; ?>>Terminé</option>
                            <option value="annule" <?php echo ($evenement['statut'] ?? '') === 'annule' ? 'selected' : ''; ?>>Annulé</option>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Inscription</h3>

                    <div class="form-group">
                        <label for="places_max">Nombre de places maximum</label>
                        <input type="number" id="places_max" name="places_max" min="0"
                               value="<?php echo $evenement['places_max'] ?? ''; ?>"
                               placeholder="0 = illimité">
                    </div>

                    <div class="form-group">
                        <label for="prix">Prix (€)</label>
                        <input type="number" id="prix" name="prix" step="0.01" min="0"
                               value="<?php echo $evenement['prix'] ?? '0'; ?>"
                               placeholder="0.00">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" name="save_evenement" class="btn btn-primary btn-full">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer' : 'Mettre à jour'; ?>
                    </button>

                    <?php if ($action === 'edit'): ?>
                        <a href="evenements.php?action=delete&id=<?php echo $id; ?>"
                           class="btn btn-danger btn-full"
                           data-action="delete" data-item="<?php echo htmlspecialchars($evenement['titre']); ?>">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </form>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
