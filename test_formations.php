<?php
// Test simple pour vérifier les formations
require_once 'includes/config.php';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Test Formations</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .formation { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test des Formations</h1>";

try {
    echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
    
    $pdo = getDBConnection();
    
    // Test de la requête exacte utilisée dans formations.php
    echo "<h2>Requête utilisée dans formations.php :</h2>";
    echo "<code>SELECT * FROM formations WHERE statut = 'active' ORDER BY secteur, niveau</code>";
    
    $stmt = $pdo->query("SELECT * FROM formations WHERE statut = 'active' ORDER BY secteur, niveau");
    $formations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='success'>✅ Requête exécutée avec succès</div>";
    echo "<p><strong>Nombre de formations trouvées :</strong> " . count($formations) . "</p>";
    
    if (empty($formations)) {
        echo "<div class='error'>❌ Aucune formation trouvée !</div>";
        
        // Vérifier s'il y a des formations dans la table
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM formations");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<p>Total formations dans la table : $total</p>";
        
        if ($total > 0) {
            // Vérifier les statuts
            $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM formations GROUP BY statut");
            $statuts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<h3>Répartition par statut :</h3>";
            foreach ($statuts as $statut) {
                echo "<p>- " . $statut['statut'] . " : " . $statut['count'] . "</p>";
            }
            
            // Afficher quelques formations pour debug
            $stmt = $pdo->query("SELECT id, nom, statut FROM formations LIMIT 5");
            $debug_formations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<h3>Premières formations (debug) :</h3>";
            foreach ($debug_formations as $f) {
                echo "<p>ID: {$f['id']}, Nom: {$f['nom']}, Statut: {$f['statut']}</p>";
            }
        } else {
            echo "<div class='error'>❌ La table formations est vide ! Vous devez installer les données.</div>";
            echo "<p><a href='database/install_database.php'>Installer la base de données</a></p>";
        }
    } else {
        echo "<h2>Formations trouvées :</h2>";
        
        foreach ($formations as $formation) {
            echo "<div class='formation'>";
            echo "<h3>" . htmlspecialchars($formation['nom']) . "</h3>";
            echo "<p><strong>Secteur :</strong> " . htmlspecialchars($formation['secteur']) . "</p>";
            echo "<p><strong>Niveau :</strong> " . htmlspecialchars($formation['niveau']) . "</p>";
            echo "<p><strong>Durée :</strong> " . htmlspecialchars($formation['duree']) . "</p>";
            echo "<p><strong>Statut :</strong> " . htmlspecialchars($formation['statut']) . "</p>";
            echo "<p><strong>Description :</strong> " . htmlspecialchars(substr($formation['description'], 0, 200)) . "...</p>";
            echo "</div>";
        }
        
        echo "<div class='success'>✅ Les formations sont correctement récupérées !</div>";
        echo "<p>Le problème ne vient pas de la base de données.</p>";
        echo "<p><a href='formations.php'>Tester la page formations.php</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur : " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<p>Vérifiez que :</p>";
    echo "<ul>";
    echo "<li>MySQL est démarré</li>";
    echo "<li>La base de données 'pole_industrie' existe</li>";
    echo "<li>Les tables sont créées</li>";
    echo "</ul>";
    echo "<p><a href='database/install_database.php'>Installer la base de données</a></p>";
}

echo "</body></html>";
?>
