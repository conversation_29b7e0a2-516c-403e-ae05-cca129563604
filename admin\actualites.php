<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification
requireLogin();

$page_title = "Gestion des actualités";
$current_user = getCurrentUser();

// Gestion des actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

try {
    $pdo = getDBConnection();
    
    // Traitement des actions POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['bulk_action']) && isset($_POST['selected_items'])) {
            // Actions en lot
            $bulk_action = $_POST['bulk_action'];
            $selected_items = $_POST['selected_items'];
            
            foreach ($selected_items as $item_id) {
                switch ($bulk_action) {
                    case 'publish':
                        $stmt = $pdo->prepare("UPDATE actualites SET statut = 'publie' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'draft':
                        $stmt = $pdo->prepare("UPDATE actualites SET statut = 'brouillon' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'delete':
                        $stmt = $pdo->prepare("DELETE FROM actualites WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                }
            }
            $message = "Actions appliquées avec succès";
        }
    }
    
    // Traitement des actions GET
    if ($action === 'delete' && $id) {
        $stmt = $pdo->prepare("DELETE FROM actualites WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Actualité supprimée avec succès";
        $action = 'list';
    } elseif ($action === 'toggle_status' && $id) {
        $stmt = $pdo->prepare("UPDATE actualites SET statut = CASE WHEN statut = 'publie' THEN 'brouillon' ELSE 'publie' END WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Statut modifié avec succès";
        $action = 'list';
    }
    
    // Récupération des actualités pour la liste
    if ($action === 'list') {
        $filter_status = $_GET['status'] ?? '';
        $filter_category = $_GET['category'] ?? '';
        $search = $_GET['search'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if ($filter_status) {
            $where_conditions[] = "statut = ?";
            $params[] = $filter_status;
        }
        
        if ($filter_category) {
            $where_conditions[] = "categorie = ?";
            $params[] = $filter_category;
        }
        
        if ($search) {
            $where_conditions[] = "(titre LIKE ? OR contenu LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $stmt = $pdo->prepare("
            SELECT a.*, u.username as created_by_name 
            FROM actualites a 
            LEFT JOIN admin_users u ON a.created_by = u.id 
            $where_clause 
            ORDER BY a.created_at DESC
        ");
        $stmt->execute($params);
        $actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = "Erreur de base de données : " . $e->getMessage();
}

include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success" data-dismiss="auto">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <div class="page-header">
        <div class="page-title">
            <h1>Gestion des actualités</h1>
            <p>Gérez les actualités et articles de votre site</p>
        </div>
        <div class="page-actions">
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Ajouter une actualité
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="filters-bar">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text" name="search" placeholder="Rechercher..." 
                       value="<?php echo htmlspecialchars($search ?? ''); ?>">
            </div>
            
            <div class="filter-group">
                <select name="status" class="status-filter">
                    <option value="">Tous les statuts</option>
                    <option value="publie" <?php echo ($filter_status === 'publie') ? 'selected' : ''; ?>>Publié</option>
                    <option value="brouillon" <?php echo ($filter_status === 'brouillon') ? 'selected' : ''; ?>>Brouillon</option>
                    <option value="archive" <?php echo ($filter_status === 'archive') ? 'selected' : ''; ?>>Archivé</option>
                </select>
            </div>
            
            <div class="filter-group">
                <select name="category">
                    <option value="">Toutes les catégories</option>
                    <option value="mecanique" <?php echo ($filter_category === 'mecanique') ? 'selected' : ''; ?>>Mécanique</option>
                    <option value="electricite" <?php echo ($filter_category === 'electricite') ? 'selected' : ''; ?>>Électricité</option>
                    <option value="automobile" <?php echo ($filter_category === 'automobile') ? 'selected' : ''; ?>>Automobile</option>
                    <option value="navale" <?php echo ($filter_category === 'navale') ? 'selected' : ''; ?>>Navale</option>
                    <option value="thermique" <?php echo ($filter_category === 'thermique') ? 'selected' : ''; ?>>Génie Thermique</option>
                    <option value="general" <?php echo ($filter_category === 'general') ? 'selected' : ''; ?>>Général</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-search"></i>
                Filtrer
            </button>
        </form>
    </div>
    
    <!-- Actions en lot -->
    <form method="POST" id="bulk-form">
        <div class="bulk-actions" style="display: none;">
            <select name="bulk_action">
                <option value="">Actions en lot</option>
                <option value="publish">Publier</option>
                <option value="draft">Mettre en brouillon</option>
                <option value="delete">Supprimer</option>
            </select>
            <button type="submit" class="btn btn-secondary">Appliquer</button>
        </div>
        
        <!-- Tableau des actualités -->
        <div class="data-table">
            <table>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all"></th>
                        <th>Titre</th>
                        <th>Date</th>
                        <th>Catégorie</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($actualites)): ?>
                        <tr>
                            <td colspan="6" class="no-data">
                                <i class="fas fa-newspaper"></i>
                                <p>Aucune actualité trouvée</p>
                                <a href="?action=add" class="btn btn-primary">Créer la première actualité</a>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($actualites as $actualite): ?>
                            <tr class="data-row" data-status="<?php echo $actualite['statut']; ?>">
                                <td>
                                    <input type="checkbox" name="selected_items[]" value="<?php echo $actualite['id']; ?>" class="item-checkbox">
                                </td>
                                <td>
                                    <div class="item-title">
                                        <strong><?php echo htmlspecialchars($actualite['titre']); ?></strong>
                                        <?php if ($actualite['featured']): ?>
                                            <span class="badge featured">À la une</span>
                                        <?php endif; ?>
                                        <div class="item-meta">
                                            Par <?php echo htmlspecialchars($actualite['created_by_name'] ?? 'Inconnu'); ?>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($actualite['created_at'])); ?></td>
                                <td>
                                    <span class="category-badge <?php echo $actualite['categorie']; ?>">
                                        <?php echo ucfirst($actualite['categorie']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $actualite['statut']; ?>">
                                        <?php echo ucfirst($actualite['statut']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=edit&id=<?php echo $actualite['id']; ?>" class="btn-icon" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="?action=toggle_status&id=<?php echo $actualite['id']; ?>" class="btn-icon" title="Changer le statut">
                                            <i class="fas fa-toggle-<?php echo $actualite['statut'] === 'publie' ? 'on' : 'off'; ?>"></i>
                                        </a>
                                        <a href="?action=delete&id=<?php echo $actualite['id']; ?>" class="btn-icon btn-danger" 
                                           data-action="delete" data-item="<?php echo htmlspecialchars($actualite['titre']); ?>" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </form>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <?php
    $actualite = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM actualites WHERE id = ?");
        $stmt->execute([$id]);
        $actualite = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$actualite) {
            $error = "Actualité non trouvée";
            $action = 'list';
        }
    }

    // Traitement du formulaire
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_actualite'])) {
        $titre = trim($_POST['titre'] ?? '');
        $contenu = trim($_POST['contenu'] ?? '');
        $extrait = trim($_POST['extrait'] ?? '');
        $categorie = $_POST['categorie'] ?? 'general';
        $statut = $_POST['statut'] ?? 'brouillon';
        $featured = isset($_POST['featured']) ? 1 : 0;
        $date_publication = $_POST['date_publication'] ?? null;

        if (empty($titre) || empty($contenu)) {
            $error = "Le titre et le contenu sont obligatoires";
        } else {
            try {
                if ($action === 'add') {
                    $stmt = $pdo->prepare("
                        INSERT INTO actualites (titre, contenu, extrait, categorie, statut, featured, date_publication, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$titre, $contenu, $extrait, $categorie, $statut, $featured, $date_publication, $current_user['id']]);
                    $message = "Actualité créée avec succès";
                } else {
                    $stmt = $pdo->prepare("
                        UPDATE actualites
                        SET titre = ?, contenu = ?, extrait = ?, categorie = ?, statut = ?, featured = ?, date_publication = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$titre, $contenu, $extrait, $categorie, $statut, $featured, $date_publication, $id]);
                    $message = "Actualité modifiée avec succès";
                }

                // Redirection après sauvegarde
                header("Location: actualites.php?message=" . urlencode($message));
                exit;

            } catch (PDOException $e) {
                $error = "Erreur lors de la sauvegarde : " . $e->getMessage();
            }
        }
    }
    ?>

    <div class="page-header">
        <div class="page-title">
            <h1><?php echo $action === 'add' ? 'Ajouter' : 'Modifier'; ?> une actualité</h1>
            <p>Créez ou modifiez le contenu de votre actualité</p>
        </div>
        <div class="page-actions">
            <a href="actualites.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Retour à la liste
            </a>
        </div>
    </div>

    <form method="POST" class="form-container" data-autosave="actualite_<?php echo $id ?? 'new'; ?>">
        <div class="form-grid">
            <div class="form-main">
                <div class="form-section">
                    <h3>Contenu principal</h3>

                    <div class="form-group">
                        <label for="titre">Titre *</label>
                        <input type="text" id="titre" name="titre" required
                               value="<?php echo htmlspecialchars($actualite['titre'] ?? ''); ?>"
                               placeholder="Titre de l'actualité">
                    </div>

                    <div class="form-group">
                        <label for="extrait">Extrait</label>
                        <textarea id="extrait" name="extrait" rows="3"
                                  placeholder="Résumé court de l'actualité (optionnel)"><?php echo htmlspecialchars($actualite['extrait'] ?? ''); ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="contenu">Contenu *</label>
                        <textarea id="contenu" name="contenu" rows="15" required class="rich-editor"
                                  placeholder="Contenu complet de l'actualité"><?php echo htmlspecialchars($actualite['contenu'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <div class="form-sidebar">
                <div class="form-section">
                    <h3>Publication</h3>

                    <div class="form-group">
                        <label for="statut">Statut</label>
                        <select id="statut" name="statut">
                            <option value="brouillon" <?php echo ($actualite['statut'] ?? '') === 'brouillon' ? 'selected' : ''; ?>>Brouillon</option>
                            <option value="publie" <?php echo ($actualite['statut'] ?? '') === 'publie' ? 'selected' : ''; ?>>Publié</option>
                            <option value="archive" <?php echo ($actualite['statut'] ?? '') === 'archive' ? 'selected' : ''; ?>>Archivé</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="date_publication">Date de publication</label>
                        <input type="date" id="date_publication" name="date_publication"
                               value="<?php echo $actualite['date_publication'] ?? date('Y-m-d'); ?>">
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="featured" value="1"
                                   <?php echo ($actualite['featured'] ?? false) ? 'checked' : ''; ?>>
                            <span class="checkmark"></span>
                            Mettre à la une
                        </label>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Catégorie</h3>

                    <div class="form-group">
                        <label for="categorie">Secteur</label>
                        <select id="categorie" name="categorie">
                            <option value="general" <?php echo ($actualite['categorie'] ?? '') === 'general' ? 'selected' : ''; ?>>Général</option>
                            <option value="mecanique" <?php echo ($actualite['categorie'] ?? '') === 'mecanique' ? 'selected' : ''; ?>>Mécanique</option>
                            <option value="electricite" <?php echo ($actualite['categorie'] ?? '') === 'electricite' ? 'selected' : ''; ?>>Électricité</option>
                            <option value="automobile" <?php echo ($actualite['categorie'] ?? '') === 'automobile' ? 'selected' : ''; ?>>Automobile</option>
                            <option value="navale" <?php echo ($actualite['categorie'] ?? '') === 'navale' ? 'selected' : ''; ?>>Navale</option>
                            <option value="thermique" <?php echo ($actualite['categorie'] ?? '') === 'thermique' ? 'selected' : ''; ?>>Génie Thermique</option>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" name="save_actualite" class="btn btn-primary btn-full">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer' : 'Mettre à jour'; ?>
                    </button>

                    <?php if ($action === 'edit'): ?>
                        <a href="actualites.php?action=delete&id=<?php echo $id; ?>"
                           class="btn btn-danger btn-full"
                           data-action="delete" data-item="<?php echo htmlspecialchars($actualite['titre']); ?>">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </form>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
