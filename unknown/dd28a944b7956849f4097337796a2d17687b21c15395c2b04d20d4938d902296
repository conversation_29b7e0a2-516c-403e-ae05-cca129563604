<?php
// Script d'installation de la base de données
require_once '../includes/config.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Connexion sans spécifier la base de données
        $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Créer la base de données si elle n'existe pas
        $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8 COLLATE utf8_general_ci");
        $pdo->exec("USE " . DB_NAME);

        // Créer les tables directement
        $sql = "
        -- Table des utilisateurs administrateurs
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'editor') DEFAULT 'editor',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL
        );

        -- Table des actualités
        CREATE TABLE IF NOT EXISTS actualites (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            contenu TEXT NOT NULL,
            extrait TEXT,
            image VARCHAR(255),
            categorie ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') DEFAULT 'general',
            statut ENUM('brouillon', 'publie', 'archive') DEFAULT 'brouillon',
            featured BOOLEAN DEFAULT FALSE,
            date_publication DATE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
        );

        -- Table des formations
        CREATE TABLE IF NOT EXISTS formations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            duree VARCHAR(50),
            niveau ENUM('CAP', 'BTS', 'Licence Pro', 'Formation Continue', 'Formation Courte', 'Apprentissage') NOT NULL,
            secteur ENUM('mecanique', 'electricite', 'automobile', 'navale', 'thermique', 'general') NOT NULL,
            prix DECIMAL(10,2),
            places_disponibles INT DEFAULT 0,
            date_debut DATE,
            date_fin DATE,
            lieu VARCHAR(255),
            statut ENUM('active', 'inactive', 'complete') DEFAULT 'active',
            image VARCHAR(255),
            brochure VARCHAR(255),
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
        );

        -- Table des événements
        CREATE TABLE IF NOT EXISTS evenements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            date_debut DATETIME NOT NULL,
            date_fin DATETIME,
            lieu VARCHAR(255),
            type_evenement ENUM('conference', 'salon', 'portes_ouvertes', 'formation', 'autre') DEFAULT 'autre',
            statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
            places_max INT,
            places_reservees INT DEFAULT 0,
            prix DECIMAL(10,2) DEFAULT 0,
            image VARCHAR(255),
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
        );

        -- Table des emplois du temps
        CREATE TABLE IF NOT EXISTS emplois_temps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            formation_id INT,
            titre VARCHAR(255) NOT NULL,
            description TEXT,
            date_cours DATE NOT NULL,
            heure_debut TIME NOT NULL,
            heure_fin TIME NOT NULL,
            salle VARCHAR(100),
            formateur VARCHAR(255),
            type_cours ENUM('cours', 'tp', 'td', 'examen', 'stage') DEFAULT 'cours',
            statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
        );
        ";

        // Exécuter les requêtes
        $pdo->exec($sql);

        // Insérer l'utilisateur admin par défaut
        $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT), 'admin']);

        $message = "Base de données installée avec succès ! Vous pouvez maintenant vous connecter avec : admin / admin123";

    } catch (PDOException $e) {
        $error = "Erreur lors de l'installation : " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-login">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-industry"></i>
                    <h1><?php echo SITE_NAME; ?></h1>
                </div>
                <h2>Installation de la base de données</h2>
                <p>Configurez votre base de données pour l'administration</p>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <div style="padding: 2rem; text-align: center;">
                    <a href="login.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Aller à la connexion
                    </a>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="login-form">
                    <div class="form-group">
                        <label>Configuration actuelle :</label>
                        <ul style="margin: 0.5rem 0; padding-left: 1.5rem; color: #666;">
                            <li>Serveur : <?php echo DB_HOST; ?></li>
                            <li>Base de données : <?php echo DB_NAME; ?></li>
                            <li>Utilisateur : <?php echo DB_USER; ?></li>
                        </ul>
                        <small>Modifiez ces paramètres dans includes/config.php si nécessaire</small>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-database"></i>
                        Installer la base de données
                    </button>
                </form>
            <?php endif; ?>
            
            <div class="login-footer">
                <p><a href="../index.php"><i class="fas fa-arrow-left"></i> Retour au site</a></p>
            </div>
        </div>
    </div>
</body>
</html>
