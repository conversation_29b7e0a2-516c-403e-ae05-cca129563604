<?php
// Test de connexion à la base de données
require_once '../includes/config.php';

echo "<h2>Test de connexion à la base de données</h2>";

try {
    echo "<p>Tentative de connexion...</p>";
    
    // Test de connexion
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✓ Connexion réussie à la base de données !</p>";
    
    // Vérifier les tables
    echo "<h3>Vérification des tables :</h3>";
    $tables = ['admin_users', 'actualites', 'formations', 'evenements', 'emplois_temps'];
    
    foreach ($tables as $table) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' existe</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' manquante</p>";
        }
    }
    
    // Vérifier l'utilisateur admin
    echo "<h3>Vérification de l'utilisateur admin :</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "<p style='color: green;'>✓ Utilisateur admin existe</p>";
    } else {
        echo "<p style='color: red;'>✗ Utilisateur admin manquant</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='install.php'>Réinstaller la base de données</a></p>";
    echo "<p><a href='login.php'>Aller à la connexion</a></p>";
    echo "<p><a href='../index.php'>Retour au site</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erreur : " . $e->getMessage() . "</p>";
    echo "<p><a href='install.php'>Installer la base de données</a></p>";
}
?>
