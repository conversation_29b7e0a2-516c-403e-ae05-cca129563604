<?php
// Script de réinitialisation du mot de passe admin
// À utiliser uniquement en cas d'urgence
// SUPPRIMER CE FICHIER APRÈS UTILISATION

require_once '../includes/config.php';

try {
    $pdo = getDBConnection();
    
    // Nouveau mot de passe : admin123
    $new_password = 'admin123';
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Mettre à jour le mot de passe de l'utilisateur admin
    $stmt = $pdo->prepare("UPDATE admin_users SET password = ? WHERE username = 'admin'");
    $result = $stmt->execute([$hashed_password]);
    
    if ($result && $stmt->rowCount() > 0) {
        echo "<h2>✅ Mot de passe réinitialisé avec succès !</h2>";
        echo "<p><strong>Nom d'utilisateur :</strong> admin</p>";
        echo "<p><strong>Nouveau mot de passe :</strong> admin123</p>";
        echo "<p><a href='login.php'>Se connecter maintenant</a></p>";
        echo "<hr>";
        echo "<p style='color: red;'><strong>IMPORTANT :</strong> Supprimez ce fichier après utilisation pour des raisons de sécurité !</p>";
    } else {
        echo "<h2>❌ Erreur : Utilisateur 'admin' non trouvé</h2>";
        
        // Vérifier si l'utilisateur existe
        $check_stmt = $pdo->prepare("SELECT username, email FROM admin_users WHERE username = 'admin'");
        $check_stmt->execute();
        $user = $check_stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo "<p>L'utilisateur 'admin' n'existe pas. Création en cours...</p>";
            
            // Créer l'utilisateur admin
            $create_stmt = $pdo->prepare("INSERT INTO admin_users (username, email, password, role) VALUES (?, ?, ?, ?)");
            $create_result = $create_stmt->execute(['admin', '<EMAIL>', $hashed_password, 'admin']);
            
            if ($create_result) {
                echo "<h2>✅ Utilisateur admin créé avec succès !</h2>";
                echo "<p><strong>Nom d'utilisateur :</strong> admin</p>";
                echo "<p><strong>Mot de passe :</strong> admin123</p>";
                echo "<p><a href='login.php'>Se connecter maintenant</a></p>";
            } else {
                echo "<h2>❌ Erreur lors de la création de l'utilisateur admin</h2>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<h2>❌ Erreur de base de données</h2>";
    echo "<p>Erreur : " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Vérifiez que :</p>";
    echo "<ul>";
    echo "<li>MySQL est démarré</li>";
    echo "<li>La base de données 'pole_industrie' existe</li>";
    echo "<li>La table 'admin_users' existe</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réinitialisation du mot de passe admin</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        h2 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="warning">
        <strong>⚠️ Attention :</strong> Ce script est destiné à la réinitialisation d'urgence du mot de passe admin. 
        Supprimez ce fichier après utilisation pour des raisons de sécurité.
    </div>
</body>
</html>
