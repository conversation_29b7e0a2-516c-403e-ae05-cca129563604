<?php
/**
 * Script d'installation de la base de données complète
 * Pôle Industrie - Installation automatique
 */

// Configuration de la base de données
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'pole_industrie';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Installation Base de Données - Pôle Industrie</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        h1 { color: #1e3a8a; text-align: center; margin-bottom: 30px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #1e3a8a; background: #f8fafc; }
        .success { border-left-color: #16a34a; background: #f0fdf4; color: #166534; }
        .error { border-left-color: #dc2626; background: #fef2f2; color: #991b1b; }
        .info { border-left-color: #0ea5e9; background: #eff6ff; color: #1e40af; }
        .code { background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
        .btn { display: inline-block; padding: 12px 24px; background: #1e3a8a; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background: #1e40af; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat { background: #f8fafc; padding: 15px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #1e3a8a; }
        .stat-label { color: #6b7280; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🏭 Installation Base de Données Pôle Industrie</h1>";

try {
    // Étape 1: Connexion à MySQL
    echo "<div class='step info'>
            <h3>📡 Étape 1: Connexion à MySQL</h3>
            <p>Connexion au serveur MySQL...</p>
          </div>";
    
    $pdo = new PDO("mysql:host=$host;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='step success'>
            <h3>✅ Connexion MySQL réussie</h3>
            <p>Serveur: $host | Utilisateur: $username</p>
          </div>";

    // Étape 2: Lecture du fichier SQL
    echo "<div class='step info'>
            <h3>📄 Étape 2: Lecture du script SQL</h3>
            <p>Chargement du fichier complete_database.sql...</p>
          </div>";
    
    $sqlFile = __DIR__ . '/full_project_database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Fichier SQL non trouvé: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("Impossible de lire le fichier SQL");
    }
    
    echo "<div class='step success'>
            <h3>✅ Fichier SQL chargé</h3>
            <p>Taille: " . number_format(strlen($sql)) . " caractères</p>
          </div>";

    // Étape 3: Exécution du script
    echo "<div class='step info'>
            <h3>⚙️ Étape 3: Exécution du script</h3>
            <p>Installation de la base de données et des tables...</p>
          </div>";
    
    // Diviser le SQL en requêtes individuelles
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($queries as $query) {
        if (empty($query) || strpos($query, '--') === 0) continue;
        
        try {
            $pdo->exec($query);
            $successCount++;
        } catch (PDOException $e) {
            $errorCount++;
            echo "<div class='step error'>
                    <h4>❌ Erreur dans la requête:</h4>
                    <div class='code'>" . htmlspecialchars(substr($query, 0, 100)) . "...</div>
                    <p><strong>Erreur:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
                  </div>";
        }
    }
    
    echo "<div class='step success'>
            <h3>✅ Installation terminée</h3>
            <p>Requêtes exécutées avec succès: <strong>$successCount</strong></p>";
    if ($errorCount > 0) {
        echo "<p>Erreurs rencontrées: <strong>$errorCount</strong></p>";
    }
    echo "</div>";

    // Étape 4: Vérification des données
    echo "<div class='step info'>
            <h3>📊 Étape 4: Vérification des données</h3>
            <p>Comptage des enregistrements dans chaque table...</p>
          </div>";
    
    $pdo->exec("USE $database");
    
    $tables = [
        'admin_users' => 'Utilisateurs Admin',
        'secteurs' => 'Secteurs',
        'formations' => 'Formations',
        'actualites' => 'Actualités',
        'evenements' => 'Événements',
        'emplois_temps' => 'Emplois du Temps',
        'contacts' => 'Messages Contact',
        'inscriptions' => 'Inscriptions',
        'parametres' => 'Paramètres',
        'testimonials' => 'Témoignages',
        'partenaires' => 'Partenaires',
        'equipements' => 'Équipements',
        'formateurs' => 'Formateurs',
        'projets_etudiants' => 'Projets Étudiants'
    ];
    
    echo "<div class='stats'>";
    foreach ($tables as $table => $label) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<div class='stat'>
                    <div class='stat-number'>$count</div>
                    <div class='stat-label'>$label</div>
                  </div>";
        } catch (PDOException $e) {
            echo "<div class='stat'>
                    <div class='stat-number'>❌</div>
                    <div class='stat-label'>$label</div>
                  </div>";
        }
    }
    echo "</div>";

    // Informations de connexion
    echo "<div class='step success'>
            <h3>🔑 Informations de connexion Admin</h3>
            <div class='code'>
                URL Admin: http://localhost/projet%20salma/admin/<br>
                Nom d'utilisateur: admin<br>
                Mot de passe: admin123
            </div>
          </div>";

    echo "<div class='step info'>
            <h3>🚀 Prochaines étapes</h3>
            <p>Votre base de données est maintenant installée avec des données d'exemple. Vous pouvez:</p>
            <ul>
                <li>Accéder à l'interface d'administration</li>
                <li>Consulter les pages du site</li>
                <li>Ajouter vos propres contenus</li>
                <li>Personnaliser les paramètres</li>
            </ul>
            <p>
                <a href='../admin/' class='btn'>🔧 Administration</a>
                <a href='../index.php' class='btn'>🏠 Site Web</a>
                <a href='../contact.php' class='btn'>📧 Contact</a>
            </p>
          </div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h3>❌ Erreur d'installation</h3>
            <p><strong>Erreur:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <p><strong>Vérifiez que:</strong></p>
            <ul>
                <li>MySQL est démarré (XAMPP/WAMP)</li>
                <li>Les paramètres de connexion sont corrects</li>
                <li>L'utilisateur MySQL a les droits nécessaires</li>
            </ul>
          </div>";
}

echo "    </div>
</body>
</html>";
?>
