<?php
// Récupération de l'ID de la formation
$formation_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$formation_id) {
    header('Location: formations.php');
    exit;
}

// Récupération des détails de la formation
$formation = null;
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM formations WHERE id = ? AND statut = 'active'");
    $stmt->execute([$formation_id]);
    $formation = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$formation) {
        header('Location: formations.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: formations.php');
    exit;
}

$page_title = $formation['nom'];
$page_description = substr(strip_tags($formation['description']), 0, 160);

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1><i class="fas fa-graduation-cap"></i> <?php echo htmlspecialchars($formation['nom']); ?></h1>
            <p><?php echo htmlspecialchars(substr($formation['description'], 0, 200)) . '...'; ?></p>
            <div class="cta-buttons">
                <a href="contact.php?formation=<?php echo $formation['id']; ?>" class="btn btn-primary">
                    <i class="fas fa-envelope"></i> S'inscrire
                </a>
                <a href="formations.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour aux formations
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Détails de la formation -->
<section class="section">
    <div class="container">
        <div class="formation-detail-grid">
            <!-- Contenu principal -->
            <div class="formation-detail-content">
                <div class="formation-detail-card">
                    <h2>Description complète</h2>
                    <div class="formation-description-full">
                        <?php echo nl2br(htmlspecialchars($formation['description'])); ?>
                    </div>
                </div>
                
                <!-- Objectifs et compétences -->
                <div class="formation-detail-card">
                    <h2><i class="fas fa-target"></i> Objectifs de la formation</h2>
                    <ul class="formation-objectives">
                        <?php
                        $secteur = $formation['secteur'];
                        $objectifs = [
                            'automobile' => [
                                'Maîtriser les techniques de diagnostic automobile',
                                'Effectuer la maintenance préventive et corrective',
                                'Utiliser les outils de diagnostic électronique',
                                'Respecter les procédures de sécurité',
                                'Conseiller la clientèle'
                            ],
                            'electricite' => [
                                'Concevoir des installations électriques',
                                'Programmer des automates industriels',
                                'Maintenir les équipements électrotechniques',
                                'Respecter les normes de sécurité électrique',
                                'Optimiser la consommation énergétique'
                            ],
                            'mecanique' => [
                                'Maîtriser les techniques d\'usinage',
                                'Effectuer la maintenance mécanique',
                                'Lire et interpréter les plans techniques',
                                'Utiliser les machines-outils',
                                'Contrôler la qualité des pièces'
                            ],
                            'thermique' => [
                                'Dimensionner les installations thermiques',
                                'Optimiser l\'efficacité énergétique',
                                'Maîtriser les énergies renouvelables',
                                'Effectuer des audits énergétiques',
                                'Respecter la réglementation thermique'
                            ],
                            'navale' => [
                                'Maîtriser les techniques de construction navale',
                                'Effectuer la soudage naval',
                                'Lire les plans de construction',
                                'Respecter les normes maritimes',
                                'Contrôler la qualité des assemblages'
                            ],
                            'general' => [
                                'Acquérir les compétences techniques',
                                'Développer l\'autonomie professionnelle',
                                'Maîtriser les outils industriels',
                                'Respecter les consignes de sécurité',
                                'Travailler en équipe'
                            ]
                        ];
                        
                        $objectifs_formation = $objectifs[$secteur] ?? $objectifs['general'];
                        foreach ($objectifs_formation as $objectif): ?>
                            <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($objectif); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Débouchés -->
                <div class="formation-detail-card">
                    <h2><i class="fas fa-briefcase"></i> Débouchés professionnels</h2>
                    <div class="debouches-grid">
                        <?php
                        $debouches = [
                            'automobile' => ['Mécanicien automobile', 'Technicien diagnostic', 'Conseiller technique', 'Chef d\'atelier'],
                            'electricite' => ['Électrotechnicien', 'Automaticien', 'Technicien maintenance', 'Chargé d\'affaires'],
                            'mecanique' => ['Usineur', 'Technicien maintenance', 'Contrôleur qualité', 'Chef d\'équipe'],
                            'thermique' => ['Technicien thermique', 'Auditeur énergétique', 'Installateur', 'Bureau d\'études'],
                            'navale' => ['Chaudronnier naval', 'Soudeur naval', 'Technicien naval', 'Chef d\'équipe'],
                            'general' => ['Technicien industriel', 'Opérateur qualifié', 'Agent de maîtrise', 'Responsable production']
                        ];
                        
                        $debouches_formation = $debouches[$secteur] ?? $debouches['general'];
                        foreach ($debouches_formation as $debouche): ?>
                            <div class="debouche-item">
                                <i class="fas fa-user-tie"></i>
                                <span><?php echo htmlspecialchars($debouche); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar informations -->
            <div class="formation-detail-sidebar">
                <div class="formation-info-card">
                    <h3>Informations pratiques</h3>
                    <div class="info-items">
                        <div class="info-item">
                            <i class="fas fa-graduation-cap"></i>
                            <div>
                                <strong>Niveau</strong>
                                <span><?php echo htmlspecialchars($formation['niveau']); ?></span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <strong>Durée</strong>
                                <span><?php echo htmlspecialchars($formation['duree']); ?></span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <div>
                                <strong>Début</strong>
                                <span><?php echo $formation['date_debut'] ? date('d/m/Y', strtotime($formation['date_debut'])) : 'À définir'; ?></span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <strong>Lieu</strong>
                                <span><?php echo htmlspecialchars($formation['lieu']); ?></span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-users"></i>
                            <div>
                                <strong>Places disponibles</strong>
                                <span><?php echo $formation['places_disponibles']; ?> places</span>
                            </div>
                        </div>
                        
                        <?php if ($formation['prix'] > 0): ?>
                        <div class="info-item">
                            <i class="fas fa-euro-sign"></i>
                            <div>
                                <strong>Tarif</strong>
                                <span><?php echo number_format($formation['prix'], 0, ',', ' '); ?>€</span>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="info-item">
                            <i class="fas fa-gift"></i>
                            <div>
                                <strong>Financement</strong>
                                <span>Formation gratuite</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="formation-actions-card">
                    <h3>Intéressé par cette formation ?</h3>
                    <div class="action-buttons">
                        <a href="contact.php?formation=<?php echo $formation['id']; ?>" class="btn btn-primary btn-full">
                            <i class="fas fa-envelope"></i> Demander des informations
                        </a>
                        <a href="contact.php?formation=<?php echo $formation['id']; ?>&action=inscription" class="btn btn-secondary btn-full">
                            <i class="fas fa-user-plus"></i> S'inscrire
                        </a>
                        <a href="tel:+33123456789" class="btn btn-outline btn-full">
                            <i class="fas fa-phone"></i> Nous appeler
                        </a>
                    </div>
                </div>
                
                <!-- Secteur -->
                <div class="formation-secteur-card">
                    <h3>Secteur d'activité</h3>
                    <div class="secteur-info <?php echo $formation['secteur']; ?>">
                        <i class="fas fa-<?php 
                            $icons = [
                                'mecanique' => 'cog',
                                'electricite' => 'bolt', 
                                'automobile' => 'car',
                                'navale' => 'ship',
                                'thermique' => 'thermometer-half',
                                'general' => 'tools'
                            ];
                            echo $icons[$formation['secteur']] ?? 'cog';
                        ?>"></i>
                        <span><?php 
                            $secteurs = [
                                'mecanique' => 'Mécanique Industrielle',
                                'electricite' => 'Électrotechnique', 
                                'automobile' => 'Automobile',
                                'navale' => 'Industrie Navale',
                                'thermique' => 'Génie Thermique',
                                'general' => 'Métiers de l\'Industrie'
                            ];
                            echo $secteurs[$formation['secteur']] ?? 'Industrie';
                        ?></span>
                    </div>
                    <a href="secteurs.php#<?php echo $formation['secteur']; ?>" class="btn btn-outline btn-small">
                        <i class="fas fa-arrow-right"></i> Découvrir le secteur
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Formations similaires -->
<section class="section" style="background: var(--light-gray);">
    <div class="container">
        <div class="section-title">
            <h2>Formations similaires</h2>
            <p>Découvrez d'autres formations qui pourraient vous intéresser</p>
        </div>
        
        <div class="formations-grid">
            <?php
            // Récupération des formations similaires
            try {
                $stmt = $pdo->prepare("SELECT * FROM formations WHERE secteur = ? AND id != ? AND statut = 'active' LIMIT 3");
                $stmt->execute([$formation['secteur'], $formation['id']]);
                $formations_similaires = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($formations_similaires as $formation_sim): ?>
                    <div class="formation-card <?php echo htmlspecialchars($formation_sim['secteur']); ?>">
                        <div class="formation-image">
                            <div class="formation-badge"><?php echo htmlspecialchars($formation_sim['niveau']); ?></div>
                            <i class="fas fa-<?php echo $icons[$formation_sim['secteur']] ?? 'cog'; ?> formation-icon"></i>
                        </div>
                        <div class="formation-content">
                            <h3><?php echo htmlspecialchars($formation_sim['nom']); ?></h3>
                            <p class="formation-description">
                                <?php echo htmlspecialchars(substr($formation_sim['description'], 0, 100)) . '...'; ?>
                            </p>
                            <div class="formation-actions">
                                <a href="formation-detail.php?id=<?php echo $formation_sim['id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-info-circle"></i> Plus d'infos
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach;
            } catch (PDOException $e) {
                // En cas d'erreur, on continue
            }
            ?>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
