<?php
$page_title = "Nos Formations";
$page_description = "Découvrez notre catalogue de formations industrielles : du CAP au BTS, formation continue et apprentissage";

// Récupération des formations depuis la base de données
$formations = [];
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SELECT * FROM formations WHERE statut = 'active' ORDER BY secteur, niveau");
    $formations = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // En cas d'erreur, on continue avec un tableau vide
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1><i class="fas fa-graduation-cap"></i> Nos Formations</h1>
            <p>Catalogue complet de formations pour développer vos compétences industrielles</p>
        </div>
    </div>
</section>

<!-- Formations disponibles -->
<section class="section">
    <div class="container">
        <div class="section-title">
            <h2>Formations Disponibles</h2>
            <p>Des programmes adaptés à tous les niveaux et tous les secteurs industriels</p>
        </div>
        
        <!-- Filtres -->
        <div class="formation-filters">
            <button class="filter-btn active" data-filter="all">Toutes les formations</button>
            <button class="filter-btn" data-filter="mecanique">Mécanique</button>
            <button class="filter-btn" data-filter="electricite">Électricité</button>
            <button class="filter-btn" data-filter="automobile">Automobile</button>
            <button class="filter-btn" data-filter="navale">Navale</button>
            <button class="filter-btn" data-filter="thermique">Génie Thermique</button>
        </div>
        
        <div class="formations-grid">
            <?php if (!empty($formations)): ?>
                <?php foreach ($formations as $formation): ?>
                    <div class="formation-card <?php echo htmlspecialchars($formation['secteur']); ?>">
                        <div class="formation-image">
                            <div class="formation-badge"><?php echo htmlspecialchars($formation['niveau']); ?></div>
                            <i class="fas fa-<?php
                                $icons = [
                                    'mecanique' => 'cog',
                                    'electricite' => 'bolt',
                                    'automobile' => 'car',
                                    'navale' => 'ship',
                                    'thermique' => 'thermometer-half',
                                    'general' => 'tools'
                                ];
                                echo $icons[$formation['secteur']] ?? 'cog';
                            ?> formation-icon"></i>
                        </div>
                        <div class="formation-content">
                            <h3><?php echo htmlspecialchars($formation['nom']); ?></h3>
                            <p class="formation-description">
                                <?php
                                $description = $formation['description'];
                                echo htmlspecialchars(strlen($description) > 150 ? substr($description, 0, 150) . '...' : $description);
                                ?>
                            </p>
                            <div class="formation-details">
                                <div class="formation-detail">
                                    <i class="fas fa-clock"></i>
                                    <span><?php echo htmlspecialchars($formation['duree']); ?></span>
                                </div>
                                <div class="formation-detail">
                                    <i class="fas fa-calendar"></i>
                                    <span><?php echo $formation['date_debut'] ? date('F Y', strtotime($formation['date_debut'])) : 'À définir'; ?></span>
                                </div>
                                <div class="formation-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span><?php echo htmlspecialchars($formation['lieu']); ?></span>
                                </div>
                                <?php if ($formation['prix'] > 0): ?>
                                <div class="formation-detail">
                                    <i class="fas fa-euro-sign"></i>
                                    <span><?php echo number_format($formation['prix'], 0, ',', ' '); ?>€</span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="formation-actions">
                                <a href="formation-detail.php?id=<?php echo $formation['id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-info-circle"></i> Plus d'infos
                                </a>
                                <a href="contact.php?formation=<?php echo $formation['id']; ?>" class="btn btn-secondary">
                                    <i class="fas fa-envelope"></i> S'inscrire
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="no-formations">
                    <i class="fas fa-graduation-cap"></i>
                    <h3>Aucune formation disponible</h3>
                    <p>Les formations seront bientôt disponibles. Contactez-nous pour plus d'informations.</p>
                    <a href="contact.php" class="btn btn-primary">Nous contacter</a>
                </div>
            <?php endif; ?>

        </div>
    </div>
</section>

<!-- Informations sur les formations -->
<section class="section" style="background: var(--light-gray);">
    <div class="container">
        <div class="section-title">
            <h2>Informations sur les Formations</h2>
            <p>Tout ce que vous devez savoir sur nos programmes de formation</p>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3><i class="fas fa-clipboard-list"></i> Critères d'admission</h3>
                <ul>
                    <li>Niveau d'études requis selon la formation</li>
                    <li>Entretien de motivation</li>
                    <li>Dossier de candidature complet</li>
                    <li>Tests d'aptitude si nécessaire</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-euro-sign"></i> Financement des formations</h3>
                <ul>
                    <li>Prise en charge par les OPCO</li>
                    <li>CPF (Compte Personnel de Formation)</li>
                    <li>Contrats d'apprentissage et de professionnalisation</li>
                    <li>Financement Pôle Emploi</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-certificate"></i> Certifications</h3>
                <ul>
                    <li>Diplômes d'État reconnus</li>
                    <li>Certifications professionnelles RNCP</li>
                    <li>Attestations de compétences</li>
                    <li>Habilitations spécialisées</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="section" style="background: var(--gradient-industrial); color: var(--white);">
    <div class="container">
        <div class="section-title" style="color: var(--white);">
            <h2 style="color: var(--white);">Besoin d'informations supplémentaires ?</h2>
            <p style="color: rgba(255,255,255,0.9);">Notre équipe pédagogique est à votre disposition pour vous conseiller</p>
        </div>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="contact.php" class="btn btn-primary" style="margin-right: 1rem;">
                <i class="fas fa-phone"></i> Nous contacter
            </a>
            <a href="#" class="btn btn-secondary">
                <i class="fas fa-calendar-alt"></i> Prendre rendez-vous
            </a>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
