<?php
$page_title = 'Contact';
$page_description = 'Contactez le Pôle Industrie pour vos questions, demandes d\'information ou inscriptions aux formations.';

// Traitement du formulaire de contact
$message_sent = false;
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = trim($_POST['nom'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $telephone = trim($_POST['telephone'] ?? '');
    $sujet = trim($_POST['sujet'] ?? '');
    $message = trim($_POST['message'] ?? '');
    $secteur = $_POST['secteur'] ?? '';
    
    // Validation
    if (empty($nom) || empty($email) || empty($sujet) || empty($message)) {
        $error_message = 'Veuillez remplir tous les champs obligatoires.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Veuillez entrer une adresse email valide.';
    } else {
        // Ici vous pouvez ajouter l'envoi d'email ou l'enregistrement en base de données
        // Pour l'instant, on simule l'envoi
        $message_sent = true;
        
        // Optionnel : Enregistrer en base de données
        try {
            $pdo = getDBConnection();
            $stmt = $pdo->prepare("
                INSERT INTO contacts (nom, email, telephone, sujet, message, secteur, date_creation) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$nom, $email, $telephone, $sujet, $message, $secteur]);
        } catch (PDOException $e) {
            // Si la table n'existe pas, on ignore l'erreur pour l'instant
        }
    }
}

// Fonction pour obtenir la page actuelle
function getCurrentPage() {
    return 'contact';
}

function isActivePage($page) {
    return $page === 'contact' ? 'active' : '';
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>Contactez-nous</h1>
            <p>Nous sommes là pour répondre à toutes vos questions et vous accompagner dans vos projets de formation industrielle.</p>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="section">
    <div class="container">
        <?php if ($message_sent): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="contact-grid">
            <!-- Formulaire de contact -->
            <div class="contact-form-section">
                <div class="section-title">
                    <h2>Envoyez-nous un message</h2>
                    <p>Remplissez le formulaire ci-dessous et nous vous répondrons rapidement.</p>
                </div>
                
                <form method="POST" class="contact-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="nom">
                                <i class="fas fa-user"></i>
                                Nom complet *
                            </label>
                            <input type="text" id="nom" name="nom" required 
                                   value="<?php echo htmlspecialchars($nom ?? ''); ?>"
                                   placeholder="Votre nom et prénom">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">
                                <i class="fas fa-envelope"></i>
                                Email *
                            </label>
                            <input type="email" id="email" name="email" required 
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="telephone">
                                <i class="fas fa-phone"></i>
                                Téléphone
                            </label>
                            <input type="tel" id="telephone" name="telephone" 
                                   value="<?php echo htmlspecialchars($telephone ?? ''); ?>"
                                   placeholder="Votre numéro de téléphone">
                        </div>
                        
                        <div class="form-group">
                            <label for="secteur">
                                <i class="fas fa-cogs"></i>
                                Secteur d'intérêt
                            </label>
                            <select id="secteur" name="secteur">
                                <option value="">Sélectionnez un secteur</option>
                                <option value="mecanique" <?php echo ($secteur ?? '') === 'mecanique' ? 'selected' : ''; ?>>Mécanique</option>
                                <option value="electricite" <?php echo ($secteur ?? '') === 'electricite' ? 'selected' : ''; ?>>Électricité</option>
                                <option value="thermique" <?php echo ($secteur ?? '') === 'thermique' ? 'selected' : ''; ?>>Génie Thermique</option>
                                <option value="automobile" <?php echo ($secteur ?? '') === 'automobile' ? 'selected' : ''; ?>>Automobile</option>
                                <option value="navale" <?php echo ($secteur ?? '') === 'navale' ? 'selected' : ''; ?>>Industrie Navale</option>
                                <option value="general" <?php echo ($secteur ?? '') === 'general' ? 'selected' : ''; ?>>Métiers de l'Industrie</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="sujet">
                            <i class="fas fa-tag"></i>
                            Sujet *
                        </label>
                        <input type="text" id="sujet" name="sujet" required 
                               value="<?php echo htmlspecialchars($sujet ?? ''); ?>"
                               placeholder="Objet de votre message">
                    </div>
                    
                    <div class="form-group">
                        <label for="message">
                            <i class="fas fa-comment"></i>
                            Message *
                        </label>
                        <textarea id="message" name="message" required rows="6" 
                                  placeholder="Décrivez votre demande, vos questions ou vos besoins..."><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Envoyer le message
                    </button>
                </form>
            </div>
            
            <!-- Informations de contact -->
            <div class="contact-info-section">
                <div class="section-title">
                    <h2>Nos coordonnées</h2>
                    <p>Retrouvez toutes nos informations de contact.</p>
                </div>
                
                <div class="contact-cards">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Adresse</h3>
                            <p>123 Avenue de l'Industrie<br>
                            75000 Paris, France</p>
                        </div>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Téléphone</h3>
                            <p><a href="tel:+33123456789">+33 1 23 45 67 89</a></p>
                        </div>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Email</h3>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Horaires</h3>
                            <p>Lundi - Vendredi : 8h00 - 18h00<br>
                            Samedi : 9h00 - 12h00</p>
                        </div>
                    </div>
                </div>
                
                <!-- Réseaux sociaux -->
                <div class="social-section">
                    <h3>Suivez-nous</h3>
                    <div class="social-links">
                        <a href="#" title="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" title="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section" style="background: var(--light-gray);">
    <div class="container">
        <div class="section-title">
            <h2>Questions fréquentes</h2>
            <p>Trouvez rapidement les réponses aux questions les plus courantes.</p>
        </div>
        
        <div class="faq-grid">
            <div class="faq-item">
                <h3><i class="fas fa-question-circle"></i> Comment s'inscrire à une formation ?</h3>
                <p>Vous pouvez vous inscrire directement via notre formulaire de contact en précisant la formation qui vous intéresse, ou nous appeler au +33 1 23 45 67 89.</p>
            </div>
            
            <div class="faq-item">
                <h3><i class="fas fa-question-circle"></i> Quels sont les prérequis pour les formations ?</h3>
                <p>Les prérequis varient selon les formations. Consultez la page détaillée de chaque formation ou contactez-nous pour plus d'informations.</p>
            </div>
            
            <div class="faq-item">
                <h3><i class="fas fa-question-circle"></i> Proposez-vous des formations en entreprise ?</h3>
                <p>Oui, nous proposons des formations sur mesure directement dans vos locaux. Contactez-nous pour établir un devis personnalisé.</p>
            </div>
            
            <div class="faq-item">
                <h3><i class="fas fa-question-circle"></i> Les formations sont-elles certifiantes ?</h3>
                <p>Toutes nos formations délivrent des certificats reconnus par les professionnels du secteur industriel.</p>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
